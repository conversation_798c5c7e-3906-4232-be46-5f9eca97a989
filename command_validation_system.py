#!/usr/bin/env python3
"""
Sistema de Validación Robusto para Comandos del Bot Paimon
Decoradores y funciones para mejorar la confiabilidad de todos los comandos
"""

import functools
import asyncio
import logging
import traceback
from typing import Any, Callable, Optional, Union, List
from datetime import datetime, timedelta
import discord
from discord.ext import commands

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CommandError(Exception):
    """Excepción personalizada para errores de comandos"""
    pass

class ValidationError(CommandError):
    """Error de validación de parámetros"""
    pass

class PermissionError(CommandError):
    """Error de permisos"""
    pass

# ==================== DECORADORES DE VALIDACIÓN ====================

def safe_command(error_message: str = "❌ Ocurrió un error inesperado"):
    """
    Decorador que envuelve comandos con manejo de errores robusto
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(ctx, *args, **kwargs):
            try:
                # Log del comando ejecutado
                logger.info(f"Ejecutando comando: {func.__name__} por {ctx.author} en {ctx.guild}")
                
                # Ejecutar el comando
                result = await func(ctx, *args, **kwargs)
                
                # Log de éxito
                logger.info(f"Comando {func.__name__} ejecutado exitosamente")
                return result
                
            except ValidationError as e:
                # Error de validación - mostrar mensaje específico
                embed = create_error_embed(
                    title="❌ Error de Validación",
                    description=str(e),
                    color=0xFF0000
                )
                await ctx.send(embed=embed)
                logger.warning(f"Error de validación en {func.__name__}: {e}")
                
            except PermissionError as e:
                # Error de permisos
                embed = create_error_embed(
                    title="🚫 Sin Permisos",
                    description=str(e),
                    color=0xFF6B00
                )
                await ctx.send(embed=embed)
                logger.warning(f"Error de permisos en {func.__name__}: {e}")
                
            except discord.Forbidden:
                # Error de permisos de Discord
                embed = create_error_embed(
                    title="🚫 Permisos Insuficientes",
                    description="El bot no tiene permisos suficientes para realizar esta acción.",
                    color=0xFF6B00
                )
                await ctx.send(embed=embed)
                logger.error(f"Discord Forbidden en {func.__name__}")
                
            except discord.NotFound:
                # Recurso no encontrado
                embed = create_error_embed(
                    title="❓ No Encontrado",
                    description="El recurso solicitado no fue encontrado.",
                    color=0xFFFF00
                )
                await ctx.send(embed=embed)
                logger.error(f"Discord NotFound en {func.__name__}")
                
            except Exception as e:
                # Error genérico
                embed = create_error_embed(
                    title="💥 Error Interno",
                    description=error_message,
                    color=0xFF0000
                )
                embed.add_field(
                    name="🔧 Información Técnica",
                    value=f"```{str(e)[:100]}...```" if len(str(e)) > 100 else f"```{str(e)}```",
                    inline=False
                )
                await ctx.send(embed=embed)
                
                # Log completo del error
                logger.error(f"Error en comando {func.__name__}: {e}")
                logger.error(traceback.format_exc())
                
        return wrapper
    return decorator

def validate_parameters(**validations):
    """
    Decorador para validar parámetros de comandos
    
    Ejemplo:
    @validate_parameters(
        amount=lambda x: x > 0 and x <= 10000,
        user=lambda x: x is not None
    )
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(ctx, *args, **kwargs):
            # Obtener nombres de parámetros de la función
            import inspect
            sig = inspect.signature(func)
            param_names = list(sig.parameters.keys())[1:]  # Excluir 'ctx'
            
            # Crear diccionario de parámetros actuales
            current_params = {}
            for i, arg in enumerate(args):
                if i < len(param_names):
                    current_params[param_names[i]] = arg
            current_params.update(kwargs)
            
            # Validar cada parámetro
            for param_name, validator in validations.items():
                if param_name in current_params:
                    value = current_params[param_name]
                    try:
                        if not validator(value):
                            raise ValidationError(f"Parámetro '{param_name}' no es válido: {value}")
                    except Exception as e:
                        raise ValidationError(f"Error validando '{param_name}': {str(e)}")
            
            return await func(ctx, *args, **kwargs)
        return wrapper
    return decorator

def require_permissions(*permissions):
    """
    Decorador para verificar permisos específicos
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(ctx, *args, **kwargs):
            # Verificar si es owner del bot
            if await ctx.bot.is_owner(ctx.author):
                return await func(ctx, *args, **kwargs)
            
            # Verificar permisos específicos
            missing_perms = []
            for perm in permissions:
                if not getattr(ctx.author.guild_permissions, perm, False):
                    missing_perms.append(perm)
            
            if missing_perms:
                perms_text = ", ".join(missing_perms)
                raise PermissionError(f"Necesitas los siguientes permisos: {perms_text}")
            
            return await func(ctx, *args, **kwargs)
        return wrapper
    return decorator

def rate_limit(calls: int = 1, period: int = 60):
    """
    Decorador para limitar la frecuencia de uso de comandos
    """
    user_calls = {}
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(ctx, *args, **kwargs):
            user_id = ctx.author.id
            now = datetime.now()
            
            # Limpiar llamadas antiguas
            if user_id in user_calls:
                user_calls[user_id] = [
                    call_time for call_time in user_calls[user_id]
                    if now - call_time < timedelta(seconds=period)
                ]
            else:
                user_calls[user_id] = []
            
            # Verificar límite
            if len(user_calls[user_id]) >= calls:
                remaining_time = period - (now - user_calls[user_id][0]).seconds
                raise ValidationError(f"Comando en cooldown. Espera {remaining_time} segundos.")
            
            # Registrar llamada
            user_calls[user_id].append(now)
            
            return await func(ctx, *args, **kwargs)
        return wrapper
    return decorator

def require_registration(func: Callable) -> Callable:
    """
    Decorador que requiere que el usuario esté registrado
    """
    @functools.wraps(func)
    async def wrapper(ctx, *args, **kwargs):
        # Aquí iría la lógica para verificar si el usuario está registrado
        # Por ahora, asumimos que todos están registrados
        return await func(ctx, *args, **kwargs)
    return wrapper

def log_command_usage(func: Callable) -> Callable:
    """
    Decorador para registrar el uso de comandos
    """
    @functools.wraps(func)
    async def wrapper(ctx, *args, **kwargs):
        # Log detallado del uso del comando
        logger.info(f"COMANDO: {func.__name__} | USUARIO: {ctx.author} ({ctx.author.id}) | SERVIDOR: {ctx.guild} | CANAL: {ctx.channel}")
        
        start_time = datetime.now()
        result = await func(ctx, *args, **kwargs)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        logger.info(f"COMANDO COMPLETADO: {func.__name__} | TIEMPO: {execution_time:.2f}s")
        
        return result
    return wrapper

# ==================== FUNCIONES DE UTILIDAD ====================

def create_error_embed(title: str, description: str, color: int = 0xFF0000) -> discord.Embed:
    """Crea un embed de error estandarizado"""
    embed = discord.Embed(
        title=title,
        description=description,
        color=color,
        timestamp=datetime.now()
    )
    embed.set_footer(text="Sistema de Validación Paimon Bot")
    return embed

def validate_amount(amount: Any, min_val: int = 1, max_val: int = 1000000) -> bool:
    """Valida que un monto sea un número válido dentro del rango"""
    try:
        amount = int(amount)
        return min_val <= amount <= max_val
    except (ValueError, TypeError):
        return False

def validate_user(user: Any) -> bool:
    """Valida que el usuario sea válido"""
    return isinstance(user, discord.Member) and not user.bot

def validate_channel(channel: Any) -> bool:
    """Valida que el canal sea válido"""
    return isinstance(channel, (discord.TextChannel, discord.VoiceChannel))

def validate_role(role: Any) -> bool:
    """Valida que el rol sea válido"""
    return isinstance(role, discord.Role)

def validate_string(text: Any, min_length: int = 1, max_length: int = 2000) -> bool:
    """Valida que el texto sea una cadena válida"""
    return isinstance(text, str) and min_length <= len(text.strip()) <= max_length

# ==================== DECORADOR COMBINADO ====================

def robust_command(
    error_message: str = "❌ Ocurrió un error inesperado",
    permissions: List[str] = None,
    rate_limit_calls: int = None,
    rate_limit_period: int = 60,
    require_auth: bool = False,
    log_usage: bool = True,
    **param_validations
):
    """
    Decorador combinado que aplica todas las validaciones necesarias
    
    Ejemplo de uso:
    @robust_command(
        error_message="Error en comando de economía",
        permissions=["manage_guild"],
        rate_limit_calls=3,
        rate_limit_period=60,
        require_auth=True,
        log_usage=True,
        amount=lambda x: validate_amount(x, 1, 10000),
        user=validate_user
    )
    async def my_command(ctx, amount: int, user: discord.Member):
        # Lógica del comando
        pass
    """
    def decorator(func: Callable) -> Callable:
        # Aplicar decoradores en orden
        decorated_func = func
        
        # Validación de parámetros
        if param_validations:
            decorated_func = validate_parameters(**param_validations)(decorated_func)
        
        # Requerimiento de registro
        if require_auth:
            decorated_func = require_registration(decorated_func)
        
        # Rate limiting
        if rate_limit_calls:
            decorated_func = rate_limit(rate_limit_calls, rate_limit_period)(decorated_func)
        
        # Permisos
        if permissions:
            decorated_func = require_permissions(*permissions)(decorated_func)
        
        # Logging
        if log_usage:
            decorated_func = log_command_usage(decorated_func)
        
        # Manejo de errores (siempre al final)
        decorated_func = safe_command(error_message)(decorated_func)
        
        return decorated_func
    return decorator

# ==================== EJEMPLO DE USO ====================

if __name__ == "__main__":
    print("🔧 Sistema de Validación de Comandos Paimon Bot")
    print("=" * 50)
    print("✅ Decoradores disponibles:")
    print("  • @safe_command - Manejo de errores robusto")
    print("  • @validate_parameters - Validación de parámetros")
    print("  • @require_permissions - Verificación de permisos")
    print("  • @rate_limit - Limitación de frecuencia")
    print("  • @require_registration - Requerimiento de registro")
    print("  • @log_command_usage - Logging de uso")
    print("  • @robust_command - Decorador combinado")
    print("\n🚀 Sistema listo para implementar en comandos existentes")
