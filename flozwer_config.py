# Configuración para FlozWer Notifications
# Variables de entorno necesarias para las APIs

import os

# Configuración de APIs para notificaciones
TWITCH_CLIENT_ID = os.environ.get('TWITCH_CLIENT_ID', '')
TWITCH_CLIENT_SECRET = os.environ.get('TWITCH_CLIENT_SECRET', '')
YOUTUBE_API_KEY = os.environ.get('YOUTUBE_API_KEY', '')

# Configuración opcional para webhooks
DISCORD_WEBHOOK_URL = os.environ.get('DISCORD_WEBHOOK_URL', '')

# Configuración de la base de datos
DATABASE_PATH = 'flozwer_notifications.db'

# Configuración de intervalos de verificación (en minutos)
STREAM_CHECK_INTERVAL = 5  # Verificar streams cada 5 minutos
VIDEO_CHECK_INTERVAL = 15  # Verificar videos cada 15 minutos

# Configuración de límites
MAX_NOTIFICATIONS_PER_HOUR = 10
MAX_VIDEOS_PER_CHECK = 5

# Configuración de colores por plataforma
PLATFORM_COLORS = {
    'twitch': 0x9146FF,
    'youtube': 0xFF0000,
    'tiktok': 0x000000,
    'instagram': 0xE4405F
}

# Configuración de emojis por plataforma
PLATFORM_EMOJIS = {
    'twitch': '📺',
    'youtube': '🎥',
    'tiktok': '📱',
    'instagram': '📸'
}

# Configuración de tipos de contenido
CONTENT_EMOJIS = {
    'stream': '🔴',
    'video': '📹'
}

# Configuración por defecto
DEFAULT_EMBED_COLOR = '#FF6B9D'
DEFAULT_AUTO_DELETE_MINUTES = 0
DEFAULT_COOLDOWN_MINUTES = 5

# Configuración de zonas horarias
DEFAULT_TIMEZONE = 'UTC'
QUIET_HOURS_START = '23:00'
QUIET_HOURS_END = '08:00'

# Configuración de archivo
AUTO_ARCHIVE_DAYS = 30

def get_config():
    """Obtiene toda la configuración en un diccionario"""
    return {
        'twitch_client_id': TWITCH_CLIENT_ID,
        'twitch_client_secret': TWITCH_CLIENT_SECRET,
        'youtube_api_key': YOUTUBE_API_KEY,
        'discord_webhook_url': DISCORD_WEBHOOK_URL,
        'database_path': DATABASE_PATH,
        'stream_check_interval': STREAM_CHECK_INTERVAL,
        'video_check_interval': VIDEO_CHECK_INTERVAL,
        'max_notifications_per_hour': MAX_NOTIFICATIONS_PER_HOUR,
        'max_videos_per_check': MAX_VIDEOS_PER_CHECK,
        'platform_colors': PLATFORM_COLORS,
        'platform_emojis': PLATFORM_EMOJIS,
        'content_emojis': CONTENT_EMOJIS,
        'default_embed_color': DEFAULT_EMBED_COLOR,
        'default_auto_delete_minutes': DEFAULT_AUTO_DELETE_MINUTES,
        'default_cooldown_minutes': DEFAULT_COOLDOWN_MINUTES,
        'default_timezone': DEFAULT_TIMEZONE,
        'quiet_hours_start': QUIET_HOURS_START,
        'quiet_hours_end': QUIET_HOURS_END,
        'auto_archive_days': AUTO_ARCHIVE_DAYS
    }

def check_api_configuration():
    """Verifica si las APIs están configuradas correctamente"""
    config_status = {
        'twitch': bool(TWITCH_CLIENT_ID and TWITCH_CLIENT_SECRET),
        'youtube': bool(YOUTUBE_API_KEY),
        'discord_webhook': bool(DISCORD_WEBHOOK_URL)
    }
    
    return config_status

def get_missing_apis():
    """Obtiene la lista de APIs que faltan por configurar"""
    config_status = check_api_configuration()
    missing = []
    
    if not config_status['twitch']:
        missing.append('Twitch (TWITCH_CLIENT_ID, TWITCH_CLIENT_SECRET)')
    
    if not config_status['youtube']:
        missing.append('YouTube (YOUTUBE_API_KEY)')
    
    return missing

def print_configuration_help():
    """Imprime ayuda para configurar las APIs"""
    print("=== Configuración de FlozWer Notifications ===")
    print()
    print("Para usar todas las funcionalidades, configura las siguientes variables de entorno:")
    print()
    print("1. TWITCH_CLIENT_ID - ID de cliente de Twitch")
    print("2. TWITCH_CLIENT_SECRET - Secreto de cliente de Twitch")
    print("3. YOUTUBE_API_KEY - Clave de API de YouTube")
    print("4. DISCORD_WEBHOOK_URL - URL de webhook de Discord (opcional)")
    print()
    print("=== Cómo obtener las APIs ===")
    print()
    print("TWITCH:")
    print("1. Ve a https://dev.twitch.tv/console")
    print("2. Crea una nueva aplicación")
    print("3. Copia el Client ID y Client Secret")
    print()
    print("YOUTUBE:")
    print("1. Ve a https://console.cloud.google.com/")
    print("2. Crea un proyecto o selecciona uno existente")
    print("3. Habilita la YouTube Data API v3")
    print("4. Crea credenciales (API Key)")
    print()
    print("=== Variables de entorno ===")
    print()
    print("En Windows (PowerShell):")
    print("$env:TWITCH_CLIENT_ID='tu_client_id'")
    print("$env:TWITCH_CLIENT_SECRET='tu_client_secret'")
    print("$env:YOUTUBE_API_KEY='tu_api_key'")
    print()
    print("En Linux/Mac:")
    print("export TWITCH_CLIENT_ID='tu_client_id'")
    print("export TWITCH_CLIENT_SECRET='tu_client_secret'")
    print("export YOUTUBE_API_KEY='tu_api_key'")
    print()
    print("=== Estado actual ===")
    config_status = check_api_configuration()
    for api, status in config_status.items():
        print(f"{api.title()}: {'✅ Configurada' if status else '❌ No configurada'}")
    
    missing = get_missing_apis()
    if missing:
        print()
        print("APIs faltantes:")
        for api in missing:
            print(f"  - {api}")

if __name__ == "__main__":
    print_configuration_help() 