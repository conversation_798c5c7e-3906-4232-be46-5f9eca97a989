# ==========================================
# PAIMON BOT - REQUIREMENTS COMPLETO
# Versión 2.1.95 - Actualizado 2024
# ==========================================

# ==========================================
# CORE - Discord Bot Framework
# ==========================================
discord.py>=2.3.2
aiohttp>=3.8.0
python-dotenv>=1.0.0
asyncio

# ==========================================
# CRIPTOGRAFÍA Y SEGURIDAD (Cuentas Encriptadas)
# ==========================================
cryptography
pycryptodome
bcrypt>=4.0.0
passlib[bcrypt,argon2]>=1.7.4
argon2-cffi>=23.0.0

# ==========================================
# AUTENTICACIÓN Y TOKENS
# ==========================================
PyJWT>=2.8.0
python-jose[cryptography]>=3.3.0
itsdangerous>=2.1.0

# ==========================================
# BASE DE DATOS Y PERSISTENCIA
# ==========================================
sqlalchemy>=2.0.0
aiosqlite>=0.19.0
pysqlite3-binary

# ==========================================
# VALIDACIÓN Y UTILIDADES DE SEGURIDAD
# ==========================================
email-validator>=2.1.0
validators>=0.22.0
secure>=0.3.0

# ==========================================
# NETWORKING Y APIs
# ==========================================
requests
urllib3>=2.0.0
httpx>=0.25.0
beautifulsoup4

# ==========================================
# GEOMETRY DASH
# ==========================================
gd.py==0.9
wrapt==1.14.1

# ==========================================
# INTELIGENCIA ARTIFICIAL
# ==========================================
google-generativeai
openai

# ==========================================
# PROCESAMIENTO DE IMÁGENES
# ==========================================
Pillow>=9.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# ==========================================
# AUDIO Y MÚSICA
# ==========================================
yt-dlp==2023.12.30
PyNaCl>=1.5.0
pydub
ffmpeg-python>=0.2.0
pyttsx3
gTTS

# FFmpeg relacionados (mantener versiones específicas)
ffmpeg==1.4
ffmpeg-thumbnail
python-ffmpeg-video-streaming==0.1.16
static-ffmpeg==2.7
imageio-ffmpeg==0.5.1
ffmpeg-normalize==1.30.0
ffmpeg-progress-yield==0.11.1

# ==========================================
# WEB SERVER Y KEEP ALIVE
# ==========================================
flask>=2.0.0
gunicorn>=21.0.0

# ==========================================
# MONITOREO Y RENDIMIENTO
# ==========================================
psutil>=5.9.0
memory-profiler>=0.61.0
py-cpuinfo>=9.0.0

# ==========================================
# UTILIDADES DE FECHA Y TIEMPO
# ==========================================
python-dateutil>=2.8.0
pytz>=2023.3

# ==========================================
# LOGGING Y DEBUGGING
# ==========================================
colorlog>=6.7.0
rich>=13.0.0

# ==========================================
# SERIALIZACIÓN Y FORMATOS
# ==========================================
pyyaml>=6.0.0
toml>=0.10.0
msgpack>=1.0.0

# ==========================================
# GITHUB Y GIT INTEGRATION
# ==========================================
PyGithub>=1.59.0
GitPython>=3.1.0

# ==========================================
# CACHE Y OPTIMIZACIÓN
# ==========================================
cachetools>=5.3.0
diskcache>=5.6.0

# ==========================================
# COMPRESIÓN Y ARCHIVOS
# ==========================================
zipfile38>=0.0.3
rarfile>=4.0.0

# ==========================================
# TESTING (Opcional para desarrollo)
# ==========================================
pytest>=7.4.0
pytest-asyncio>=0.21.0

# ==========================================
# EXTRAS OPCIONALES (Descomenta si necesitas)
# ==========================================
 selenium>=4.15.0  # Para web scraping
 lxml>=4.9.0  # Parser XML/HTML rápido
 matplotlib>=3.7.0  # Para gráficos
 pandas>=2.1.0  # Para análisis de datos
 redis>=5.0.0  # Para cache distribuido
 celery>=5.3.0  # Para tareas asíncronas

# ==========================================
# CIFRADO PERSONALIZADO
# ==========================================
xor_cipher
