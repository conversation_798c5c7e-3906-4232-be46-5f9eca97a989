#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 Sistema de Reinicio <PERSON> - Paimon Bot
Maneja reinicios limpios con señales y cierre adecuado de conexiones
"""

import os
import sys
import signal
import asyncio
import logging
import subprocess
from datetime import datetime
from typing import Optional, Dict, Any
import discord

class RestartManager:
    """Gestor de reinicios robusto para el bot"""
    
    def __init__(self, bot: discord.Bot):
        self.bot = bot
        self.is_restarting = False
        self.restart_reason = None
        self.restart_channel = None
        self.logger = logging.getLogger('RestartManager')
        self.setup_signal_handlers()
        
    def setup_signal_handlers(self):
        """Configura manejadores de señales para cierre limpio"""
        try:
            # Solo en sistemas Unix
            if os.name != 'nt':
                signal.signal(signal.SIGTERM, self._signal_handler)
                signal.signal(signal.SIGINT, self._signal_handler)
                signal.signal(signal.SIGHUP, self._signal_handler)
            
            self.logger.info("✅ Signal handlers configurados")
        except Exception as e:
            self.logger.warning(f"⚠️ No se pudieron configurar signal handlers: {e}")
    
    def _signal_handler(self, signum, frame):
        """Maneja señales del sistema para cierre limpio"""
        signal_names = {
            signal.SIGTERM: "SIGTERM",
            signal.SIGINT: "SIGINT", 
            signal.SIGHUP: "SIGHUP"
        }
        
        signal_name = signal_names.get(signum, f"Signal {signum}")
        self.logger.info(f"📡 Señal recibida: {signal_name}")
        
        # Crear tarea para cierre limpio
        asyncio.create_task(self.graceful_shutdown(f"Señal del sistema: {signal_name}"))
    
    async def graceful_shutdown(self, reason: str = "Shutdown solicitado"):
        """Realiza un cierre limpio del bot"""
        try:
            self.logger.info(f"🔄 Iniciando cierre limpio: {reason}")
            
            # Marcar como en proceso de cierre
            self.is_restarting = True
            self.restart_reason = reason
            
            # Notificar en el canal si está disponible
            if self.restart_channel:
                try:
                    embed = discord.Embed(
                        title="🔄 Bot Cerrando",
                        description=f"Razón: {reason}",
                        color=0xFFAA00,
                        timestamp=datetime.now()
                    )
                    await self.restart_channel.send(embed=embed)
                except Exception as e:
                    self.logger.error(f"❌ Error notificando cierre: {e}")
            
            # Guardar configuraciones críticas
            await self.save_critical_data()
            
            # Cerrar conexiones del bot
            await self.close_bot_connections()
            
            self.logger.info("✅ Cierre limpio completado")
            
        except Exception as e:
            self.logger.error(f"❌ Error durante cierre limpio: {e}")
        finally:
            # Forzar salida si es necesario
            os._exit(0)
    
    async def save_critical_data(self):
        """Guarda datos críticos antes del cierre"""
        try:
            self.logger.info("💾 Guardando datos críticos...")
            
            # Guardar configuraciones de todos los servidores
            for guild in self.bot.guilds:
                try:
                    # Disparar auto-sync para cada servidor
                    from Bot import auto_sync_configuration
                    await auto_sync_configuration(str(guild.id), "bot_shutdown")
                except Exception as e:
                    self.logger.error(f"❌ Error guardando datos del servidor {guild.id}: {e}")
            
            self.logger.info("✅ Datos críticos guardados")
            
        except Exception as e:
            self.logger.error(f"❌ Error guardando datos críticos: {e}")
    
    async def close_bot_connections(self):
        """Cierra todas las conexiones del bot"""
        try:
            self.logger.info("🔌 Cerrando conexiones del bot...")
            
            # Cerrar conexión de Discord
            if not self.bot.is_closed():
                await self.bot.close()
            
            # Esperar un momento para que se cierren las conexiones
            await asyncio.sleep(2)
            
            self.logger.info("✅ Conexiones cerradas")
            
        except Exception as e:
            self.logger.error(f"❌ Error cerrando conexiones: {e}")
    
    async def restart_bot(self, channel: Optional[discord.TextChannel] = None, reason: str = "Reinicio manual"):
        """Reinicia el bot de manera segura"""
        try:
            self.restart_channel = channel
            self.logger.info(f"🔄 Iniciando reinicio: {reason}")
            
            # Notificar inicio de reinicio
            if channel:
                embed = discord.Embed(
                    title="🔄 Reiniciando Bot",
                    description=f"Razón: {reason}\n⏳ Guardando datos y cerrando conexiones...",
                    color=0x00AAFF,
                    timestamp=datetime.now()
                )
                await channel.send(embed=embed)
            
            # Detectar plataforma y método de reinicio
            restart_method = self.detect_restart_method()
            
            # Guardar datos críticos
            await self.save_critical_data()
            
            # Ejecutar reinicio según la plataforma
            if restart_method == "replit":
                await self.replit_restart()
            elif restart_method == "systemd":
                await self.systemd_restart()
            elif restart_method == "docker":
                await self.docker_restart()
            else:
                await self.standard_restart()
                
        except Exception as e:
            self.logger.error(f"❌ Error durante reinicio: {e}")
            if channel:
                error_embed = discord.Embed(
                    title="❌ Error en Reinicio",
                    description=f"Error: {str(e)}",
                    color=0xFF0000
                )
                await channel.send(embed=error_embed)
    
    def detect_restart_method(self) -> str:
        """Detecta el método de reinicio apropiado según la plataforma"""
        # Detectar Replit
        if os.getenv('REPL_ID') or os.getenv('REPLIT_DB_URL'):
            return "replit"
        
        # Detectar Docker
        if os.path.exists('/.dockerenv'):
            return "docker"
        
        # Detectar systemd
        if os.path.exists('/run/systemd/system'):
            return "systemd"
        
        # Método estándar
        return "standard"
    
    async def replit_restart(self):
        """Reinicio específico para Replit"""
        try:
            self.logger.info("🔄 Reinicio Replit detectado")
            
            # En Replit, simplemente salir permite que se reinicie automáticamente
            await self.close_bot_connections()
            
            # Crear archivo de señal para indicar reinicio
            with open('.restart_signal', 'w') as f:
                f.write(f"restart:{datetime.now().isoformat()}")
            
            # Salir limpiamente
            os._exit(0)
            
        except Exception as e:
            self.logger.error(f"❌ Error en reinicio Replit: {e}")
            raise
    
    async def systemd_restart(self):
        """Reinicio usando systemd"""
        try:
            self.logger.info("🔄 Reinicio systemd")
            
            await self.close_bot_connections()
            
            # Usar systemctl para reiniciar el servicio
            subprocess.Popen(['systemctl', 'restart', 'paimon-bot'])
            
        except Exception as e:
            self.logger.error(f"❌ Error en reinicio systemd: {e}")
            raise
    
    async def docker_restart(self):
        """Reinicio en contenedor Docker"""
        try:
            self.logger.info("🔄 Reinicio Docker")
            
            await self.close_bot_connections()
            
            # En Docker, salir con código específico para reinicio
            os._exit(42)  # Código específico para reinicio
            
        except Exception as e:
            self.logger.error(f"❌ Error en reinicio Docker: {e}")
            raise
    
    async def standard_restart(self):
        """Reinicio estándar usando exec"""
        try:
            self.logger.info("🔄 Reinicio estándar")
            
            await self.close_bot_connections()
            
            # Obtener argumentos del script actual
            python_executable = sys.executable
            script_path = sys.argv[0]
            
            # Reiniciar usando exec
            os.execv(python_executable, [python_executable, script_path] + sys.argv[1:])
            
        except Exception as e:
            self.logger.error(f"❌ Error en reinicio estándar: {e}")
            raise
    
    def is_restart_in_progress(self) -> bool:
        """Verifica si hay un reinicio en progreso"""
        return self.is_restarting
    
    def get_restart_info(self) -> Dict[str, Any]:
        """Obtiene información del reinicio actual"""
        return {
            'is_restarting': self.is_restarting,
            'reason': self.restart_reason,
            'method': self.detect_restart_method(),
            'timestamp': datetime.now().isoformat()
        }

# Función de utilidad para crear el manager
def create_restart_manager(bot: discord.Bot) -> RestartManager:
    """Crea una instancia del RestartManager"""
    return RestartManager(bot)

# Decorador para comandos que requieren reinicio seguro
def safe_restart_required(func):
    """Decorador que asegura reinicio seguro"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"❌ Error en comando de reinicio: {e}")
            raise
    return wrapper
