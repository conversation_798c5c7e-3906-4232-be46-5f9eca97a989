# Configuración para las funcionalidades de Nekotina
# Paimon Bot 2.0

# Configuración de colores para embeds
EMBED_COLORS = {
    'primary': '#5865F2',      # Azul Discord
    'success': '#57F287',      # <PERSON>
    'error': '#ED4245',        # Rojo
    'warning': '#FEE75C',      # Amarillo
    'info': '#5865F2',         # Azul
    'pink': '#FF69B4',         # Rosa
    'gold': '#FFD700',         # Dorado
    'purple': '#9B59B6',       # Púrpura
    'orange': '#FF6B35',       # Naranja
    'teal': '#1ABC9C',         # Verde azulado
    'level': '#FF6B6B',        # Rojo coral
    'star': '#FFD93D',         # Amarillo dorado
    'music': '#8B5CF6',        # Violeta
    'secondary': '#4F545C'     # Gris Discord
}

# Configuración de emojis
EMOJIS = {
    'success': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️',
    'music': '🎵',
    'level': '⭐',
    'star': '🌟',
    'pink': '💕',
    'gold': '💰',
    'red': '🛡️',
    'blue': '🔵',
    'green': '🟢',
    'yellow': '🟡',
    'purple': '🟣',
    'orange': '🟠',
    'white': '⚪',
    'black': '⚫'
}

# Configuración de GIFs para roleplay
ROLEPLAY_GIFS = {
    'hugs': [
        "https://media.giphy.com/media/143v0Z4767T15e/giphy.gif",
        "https://media.giphy.com/media/wnsgren9NtITS/giphy.gif",
        "https://media.giphy.com/media/IRUb7GTCaPU8E/giphy.gif",
        "https://media.giphy.com/media/wnsgren9NtITS/giphy.gif",
        "https://media.giphy.com/media/143v0Z4767T15e/giphy.gif",
        "https://media.giphy.com/media/16bJmyPvRbCDu/giphy.gif",
        "https://media.giphy.com/media/wnsgren9NtITS/giphy.gif",
        "https://media.giphy.com/media/143v0Z4767T15e/giphy.gif"
    ],
    'kisses': [
        "https://media.giphy.com/media/bGm9FuBCGg4SY/giphy.gif",
        "https://media.giphy.com/media/11k3oa2jbfFR0I/giphy.gif",
        "https://media.giphy.com/media/12VXIxKaIEarL2/giphy.gif",
        "https://media.giphy.com/media/wOtkVwroA6yzK/giphy.gif",
        "https://media.giphy.com/media/11k3oa2jbfFR0I/giphy.gif",
        "https://media.giphy.com/media/bGm9FuBCGg4SY/giphy.gif",
        "https://media.giphy.com/media/12VXIxKaIEarL2/giphy.gif",
        "https://media.giphy.com/media/wOtkVwroA6yzK/giphy.gif"
    ],
    'pats': [
        "https://media.giphy.com/media/ARWgZH53jHx8c/giphy.gif",
        "https://media.giphy.com/media/5tmRHwTlHax9XeNl5V/giphy.gif",
        "https://media.giphy.com/media/4HP0ddZnNVvKU/giphy.gif",
        "https://media.giphy.com/media/5tmRHwTlHax9XeNl5V/giphy.gif",
        "https://media.giphy.com/media/ARWgZH53jHx8c/giphy.gif",
        "https://media.giphy.com/media/4HP0ddZnNVvKU/giphy.gif",
        "https://media.giphy.com/media/5tmRHwTlHax9XeNl5V/giphy.gif",
        "https://media.giphy.com/media/ARWgZH53jHx8c/giphy.gif"
    ],
    'bites': [
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif"
    ],
    'slaps': [
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif",
        "https://media.giphy.com/media/3o7TKxJ9X2X2X2X2X2/giphy.gif"
    ]
}

# Configuración de economía
ECONOMY_CONFIG = {
    'daily_base': 100,           # Recompensa base diaria
    'daily_streak_bonus': 10,    # Bono por día de racha
    'daily_max_bonus': 100,      # Bono máximo por racha
    'work_min': 50,              # Mínimo por trabajo
    'work_max': 150,             # Máximo por trabajo
    'work_cooldown': 3600,       # Cooldown de trabajo (1 hora)
    'bank_interest': 0.05,       # Interés bancario (5%)
    'transfer_fee': 0.01,        # Comisión de transferencia (1%)
    'max_transfer': 10000        # Transferencia máxima
}

# Configuración de waifus
WAIFU_CONFIG = {
    'rarities': {
        'R': {'chance': 0.6, 'color': '#9B59B6'},
        'SR': {'chance': 0.3, 'color': '#3498DB'},
        'SSR': {'chance': 0.1, 'color': '#F1C40F'}
    },
    'pull_cooldown': 300,        # 5 minutos entre pulls
    'max_collection': 1000       # Máximo waifus en colección
}

# Configuración de starboard
STARBOARD_CONFIG = {
    'default_min_stars': 3,      # Estrellas mínimas por defecto
    'max_stars': 10,             # Máximo estrellas para mostrar
    'auto_delete': True,         # Auto-eliminar mensajes con pocas estrellas
    'delete_threshold': 2        # Umbral para auto-eliminar
}

# Configuración de giveaways
GIVEAWAY_CONFIG = {
    'max_duration': 604800,      # Máximo 7 días
    'min_duration': 60,          # Mínimo 1 minuto
    'max_winners': 10,           # Máximo 10 ganadores
    'min_winners': 1,            # Mínimo 1 ganador
    'auto_end': True,            # Auto-terminar sorteos
    'reroll_limit': 3            # Máximo re-sorteos
}

# Configuración de protección
PROTECTION_CONFIG = {
    'anti_spam': {
        'enabled': True,
        'threshold': 5,          # Mensajes en 10 segundos
        'time_window': 10,       # Ventana de tiempo en segundos
        'action': 'mute',        # Acción: mute, kick, ban
        'duration': 300          # Duración del mute (5 minutos)
    },
    'anti_invite': {
        'enabled': True,
        'whitelist': [],         # Servidores permitidos
        'action': 'delete',      # Acción: delete, warn, mute
        'log': True              # Registrar intentos
    },
    'anti_link': {
        'enabled': False,        # Deshabilitado por defecto
        'whitelist': [],         # Dominios permitidos
        'action': 'delete',      # Acción: delete, warn, mute
        'log': True              # Registrar intentos
    },
    'anti_raid': {
        'enabled': True,
        'join_threshold': 10,    # Usuarios en 30 segundos
        'time_window': 30,       # Ventana de tiempo
        'action': 'ban',         # Acción: ban, kick
        'duration': 86400        # Duración del ban (1 día)
    }
}

# Configuración de logging
LOGGING_CONFIG = {
    'events': {
        'message_delete': True,
        'message_edit': True,
        'member_join': True,
        'member_leave': True,
        'member_ban': True,
        'member_unban': True,
        'role_create': True,
        'role_delete': True,
        'role_update': True,
        'channel_create': True,
        'channel_delete': True,
        'channel_update': True
    },
    'format': {
        'timestamp': True,
        'user_mention': True,
        'channel_mention': True,
        'role_mention': True
    }
}

# Configuración de bienvenidas/despedidas
WELCOME_CONFIG = {
    'default_welcome': "¡Bienvenid@ {user} al servidor {server}! 🎉\nYa somos {member_count} miembros.",
    'default_goodbye': "¡Hasta luego {user}! 😢\nEsperamos verte pronto.",
    'embed_colors': {
        'welcome': '#00ff00',
        'goodbye': '#ff0000'
    },
    'variables': {
        '{user}': 'Menciona al usuario',
        '{user.name}': 'Nombre del usuario',
        '{server}': 'Nombre del servidor',
        '{member_count}': 'Número de miembros'
    }
}

# Configuración de cumpleaños
BIRTHDAY_CONFIG = {
    'notification_time': '09:00',  # Hora de notificación
    'role_duration': 86400,        # Duración del rol (1 día)
    'auto_role': True,             # Asignar rol automáticamente
    'announcement_channel': None,   # Canal de anuncios
    'embed_color': '#FF69B4'       # Color del embed
}

# Configuración de anime tracking
ANIME_CONFIG = {
    'states': {
        'watching': 'Viendo',
        'completed': 'Completado',
        'plan_to_watch': 'Planificado',
        'dropped': 'Abandonado',
        'on_hold': 'En pausa'
    },
    'max_list_size': 1000,         # Máximo animes en lista
    'auto_update': True,           # Auto-actualizar estados
    'share_lists': True            # Permitir compartir listas
}

# Configuración de mascotas
PET_CONFIG = {
    'max_pets': 5,                 # Máximo mascotas por usuario
    'feeding_cooldown': 3600,      # Cooldown de alimentación (1 hora)
    'evolution_levels': [10, 25, 50, 100],  # Niveles de evolución
    'hunger_rate': 0.1,            # Tasa de hambre por hora
    'happiness_rate': 0.05,        # Tasa de felicidad por hora
    'max_stats': 100               # Estadísticas máximas
}

# Configuración de matrimonios
MARRIAGE_CONFIG = {
    'proposal_cooldown': 86400,    # Cooldown de propuesta (1 día)
    'divorce_cooldown': 604800,    # Cooldown de divorcio (1 semana)
    'max_marriages': 1,            # Máximo matrimonios por usuario
    'ceremony_duration': 300,      # Duración de ceremonia (5 minutos)
    'benefits': {
        'daily_bonus': 1.5,        # 50% más de daily
        'transfer_fee': 0.005,     # 0.5% de comisión
        'shared_inventory': True   # Inventario compartido
    }
}

# Configuración de eventos
EVENT_CONFIG = {
    'auto_events': True,           # Eventos automáticos
    'event_duration': 3600,        # Duración por defecto (1 hora)
    'max_participants': 100,       # Máximo participantes
    'reward_multiplier': 2.0,      # Multiplicador de recompensas
    'types': {
        'roleplay': 'Evento de roleplay',
        'economy': 'Evento económico',
        'waifu': 'Evento de waifus',
        'anime': 'Evento de anime',
        'general': 'Evento general'
    }
}

# Configuración de clubs
CLUB_CONFIG = {
    'max_members': 50,             # Máximo miembros por club
    'creation_cost': 1000,         # Costo de creación
    'max_clubs': 10,               # Máximo clubs por servidor
    'hierarchy_levels': 5,         # Niveles de jerarquía
    'auto_events': True,           # Eventos automáticos de club
    'competitions': True           # Competencias entre clubs
}

# Configuración de estadísticas
STATS_CONFIG = {
    'track_messages': True,        # Rastrear mensajes
    'track_commands': True,        # Rastrear comandos
    'track_voice': True,           # Rastrear tiempo de voz
    'track_reactions': True,       # Rastrear reacciones
    'auto_backup': True,           # Backup automático
    'backup_interval': 86400,      # Intervalo de backup (1 día)
    'max_history': 30              # Días de historial
}

# Configuración de notificaciones
NOTIFICATION_CONFIG = {
    'dm_notifications': True,      # Notificaciones por DM
    'channel_notifications': True, # Notificaciones en canal
    'mention_users': True,         # Mencionar usuarios
    'embed_notifications': True,   # Usar embeds para notificaciones
    'sound_notifications': False,  # Notificaciones de sonido
    'auto_delete': 300             # Auto-eliminar después de 5 minutos
}

# Configuración de personalización
CUSTOMIZATION_CONFIG = {
    'custom_prefixes': True,       # Prefijos personalizados
    'custom_colors': True,         # Colores personalizados
    'custom_emojis': True,         # Emojis personalizados
    'custom_messages': True,       # Mensajes personalizados
    'custom_commands': True,       # Comandos personalizados
    'max_custom_commands': 10      # Máximo comandos personalizados
}

# Configuración de rendimiento
PERFORMANCE_CONFIG = {
    'cache_enabled': True,         # Habilitar cache
    'cache_size': 1000,            # Tamaño del cache
    'cache_ttl': 3600,             # TTL del cache (1 hora)
    'database_optimization': True, # Optimización de base de datos
    'query_limit': 100,            # Límite de consultas
    'auto_cleanup': True,          # Limpieza automática
    'cleanup_interval': 86400      # Intervalo de limpieza (1 día)
}

# Configuración de seguridad
SECURITY_CONFIG = {
    'rate_limiting': True,         # Limitación de tasa
    'max_requests_per_minute': 60, # Máximo requests por minuto
    'blacklist_enabled': True,     # Lista negra habilitada
    'whitelist_enabled': True,     # Lista blanca habilitada
    'encryption_enabled': True,    # Encriptación habilitada
    'backup_encryption': True,     # Encriptación de backups
    'audit_logging': True          # Logging de auditoría
}

# Configuración de desarrollo
DEVELOPMENT_CONFIG = {
    'debug_mode': False,           # Modo debug
    'verbose_logging': False,      # Logging verboso
    'test_mode': False,            # Modo de prueba
    'auto_restart': True,          # Auto-reinicio
    'error_reporting': True,       # Reporte de errores
    'performance_monitoring': True # Monitoreo de rendimiento
}

# Exportar todas las configuraciones
ALL_CONFIGS = {
    'embed_colors': EMBED_COLORS,
    'emojis': EMOJIS,
    'roleplay_gifs': ROLEPLAY_GIFS,
    'economy': ECONOMY_CONFIG,
    'waifu': WAIFU_CONFIG,
    'starboard': STARBOARD_CONFIG,
    'giveaway': GIVEAWAY_CONFIG,
    'protection': PROTECTION_CONFIG,
    'logging': LOGGING_CONFIG,
    'welcome': WELCOME_CONFIG,
    'birthday': BIRTHDAY_CONFIG,
    'anime': ANIME_CONFIG,
    'pet': PET_CONFIG,
    'marriage': MARRIAGE_CONFIG,
    'event': EVENT_CONFIG,
    'club': CLUB_CONFIG,
    'stats': STATS_CONFIG,
    'notification': NOTIFICATION_CONFIG,
    'customization': CUSTOMIZATION_CONFIG,
    'performance': PERFORMANCE_CONFIG,
    'security': SECURITY_CONFIG,
    'development': DEVELOPMENT_CONFIG
} 