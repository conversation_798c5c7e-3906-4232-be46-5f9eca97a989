#!/usr/bin/env python3
"""
Analizador Simple de Comandos del Bot Paimon
"""

import re
import os

def analyze_commands():
    """Analiza comandos del bot"""
    bot_file = "Paimon-Bot-Discord/Bot.py"
    
    if not os.path.exists(bot_file):
        print(f"❌ Archivo {bot_file} no encontrado")
        return
    
    print("🔍 Analizando comandos del bot...")
    
    with open(bot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Encontrar comandos
    pattern = r'@bot\.command\(name="([^"]+)"[^)]*\)'
    commands = re.findall(pattern, content)
    
    print(f"📊 Encontrados {len(commands)} comandos:")
    
    # Categorizar comandos
    categories = {
        "🎮 Niveles/Stage": ["queue", "skip", "remove", "nowplaying", "clearqueue", "clear", "stage", "Stage", "showstage", "del", "profile", "request"],
        "⭐ XP/Niveles": ["top", "levelroles", "setlevelrole", "resetlevel", "addpoints", "setlevelchannel", "settoprole", "setxpbased", "setcooldown", "resetxp", "rank"],
        "🎵 Música": ["playmusic", "skipmusic", "stopmusic", "queuemusic", "nowplayingmusic", "volumemusic", "loopmusic"],
        "🎤 TTS": ["paimontts", "stoptts", "ttsstatus"],
        "💰 Economía": ["balance", "daily", "work", "shop", "buy", "sell", "inventory", "give", "rob", "leaderboard", "casino", "blackjack", "crash", "marketplace"],
        "🛡️ Moderación": ["warn", "mute", "kick", "ban", "unban", "clear", "slowmode", "lock", "unlock"],
        "🔴 Stream": ["streamconfig", "streamenable", "streamstatus", "streamtest", "streamadd", "streamremove"],
        "🛡️ Anti-Raid": ["antiraid", "clearwarnings", "raidstatus"],
        "⚙️ Config": ["version", "ping", "com", "config", "sync", "backup", "restore", "setversion"],
        "🔄 Control": ["restart", "off", "hotreload"],
        "🔐 Seguridad": ["register", "login", "logout", "changepass", "deleteaccount"],
        "🎯 Counter": ["setupcounter", "counterstats", "countertop", "counterhelp", "counterevent"],
        "🌐 FlozWer": ["flozwerconfig", "flozweradd", "flozwerremove", "flozwerlist", "flozwerstatus", "flozwertest", "flozwerhelp"]
    }
    
    found_by_category = {}
    not_categorized = []
    
    for category, category_commands in categories.items():
        found_commands = [cmd for cmd in category_commands if cmd in commands]
        if found_commands:
            found_by_category[category] = found_commands
    
    # Comandos no categorizados
    all_categorized = []
    for cmds in categories.values():
        all_categorized.extend(cmds)
    
    not_categorized = [cmd for cmd in commands if cmd not in all_categorized]
    
    # Mostrar resultados
    print("\n📋 COMANDOS POR CATEGORÍA:")
    print("=" * 50)
    
    for category, found_commands in found_by_category.items():
        print(f"\n{category} ({len(found_commands)} comandos):")
        for cmd in found_commands:
            print(f"  • !{cmd}")
    
    if not_categorized:
        print(f"\n❓ OTROS COMANDOS ({len(not_categorized)} comandos):")
        for cmd in not_categorized:
            print(f"  • !{cmd}")
    
    # Análisis de problemas comunes
    print(f"\n🔍 ANÁLISIS DE PROBLEMAS:")
    print("=" * 50)
    
    issues = []
    
    # Buscar comandos sin manejo de errores
    commands_without_try = []
    for cmd in commands:
        # Buscar la función del comando
        func_pattern = rf'@bot\.command\(name="{cmd}"[^)]*\).*?async def ([^(]+)\([^)]*\):(.*?)(?=@bot\.command|$)'
        match = re.search(func_pattern, content, re.DOTALL)
        if match:
            func_code = match.group(2)
            if 'try:' not in func_code or 'except' not in func_code:
                commands_without_try.append(cmd)
    
    if commands_without_try:
        print(f"⚠️ Comandos sin manejo de errores ({len(commands_without_try)}):")
        for cmd in commands_without_try[:10]:  # Mostrar solo los primeros 10
            print(f"  • !{cmd}")
        if len(commands_without_try) > 10:
            print(f"  ... y {len(commands_without_try) - 10} más")
    
    # Comandos críticos que necesitan atención
    critical_commands = ['casino', 'marketplace', 'balance', 'daily', 'restart', 'off', 'sync']
    critical_issues = [cmd for cmd in critical_commands if cmd in commands_without_try]
    
    if critical_issues:
        print(f"\n🚨 COMANDOS CRÍTICOS SIN MANEJO DE ERRORES:")
        for cmd in critical_issues:
            print(f"  • !{cmd} - REQUIERE ATENCIÓN INMEDIATA")
    
    # Resumen final
    total_commands = len(commands)
    commands_with_issues = len(commands_without_try)
    health_score = max(0, 100 - (commands_with_issues * 5))
    
    print(f"\n📊 RESUMEN:")
    print(f"  • Total comandos: {total_commands}")
    print(f"  • Comandos con problemas: {commands_with_issues}")
    print(f"  • Score de salud: {health_score}/100")
    
    if health_score < 70:
        print("🔴 ESTADO CRÍTICO - Requiere optimización inmediata")
    elif health_score < 85:
        print("🟡 ESTADO REGULAR - Necesita mejoras")
    else:
        print("✅ ESTADO BUENO - Pocos problemas detectados")
    
    return {
        'total_commands': total_commands,
        'commands_by_category': found_by_category,
        'commands_without_error_handling': commands_without_try,
        'critical_issues': critical_issues,
        'health_score': health_score
    }

if __name__ == "__main__":
    analyze_commands()
