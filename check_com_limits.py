#!/usr/bin/env python3
"""
Script para verificar los límites de caracteres del comando !com
"""

def check_embed_limits_simple(title, description, fields, footer):
    """Verificar límites de caracteres de un embed"""
    total_chars = 0
    
    if title:
        total_chars += len(title)
    if description:
        total_chars += len(description)
    
    for field_name, field_value in fields:
        total_chars += len(field_name) + len(field_value)
    
    if footer:
        total_chars += len(footer)
    
    return {
        'total_chars': total_chars,
        'within_limits': total_chars <= 5500,
        'field_count': len(fields)
    }

# Simular las categorías principales del comando !com
categorias = {
    'gd': {
        'nombre': '🎮 Geometry Dash',
        'descripcion': 'Comandos para interactuar con Geometry Dash',
        'comandos': [
            ('!gduser <usuario>', 'Buscar información de un usuario'),
            ('!gdlevel <id>', 'Ver información de un nivel'),
            ('!gddaily', 'Ver el nivel diario actual'),
            ('!gdweekly', 'Ver el nivel semanal actual'),
            ('!gdstats <usuario>', 'Estadísticas detalladas'),
            ('!gdprofile <usuario>', 'Perfil completo de usuario'),
            ('!gdtop', 'Top jugadores del servidor'),
            ('!gdleaderboard', 'Tabla de clasificación')
        ]
    },
    'economia': {
        'nombre': '💰 Casino',
        'descripcion': 'Sistema económico del servidor',
        'comandos': [
            ('!balance', 'Ver tu dinero actual'),
            ('!daily', 'Recompensa diaria'),
            ('!casino', 'Acceder al casino principal'),
            ('!blackjack <cantidad>', 'Jugar blackjack'),
            ('!crash <cantidad>', 'Jugar crash'),
            ('!shop', 'Ver la tienda del servidor'),
            ('!buy <item>', 'Comprar items'),
            ('!marketplace', 'Mercado de usuarios')
        ]
    },
    'musica': {
        'nombre': '🎵 Música',
        'descripcion': 'Comandos de música y audio',
        'comandos': [
            ('!playmusic <canción>', 'Reproducir música'),
            ('!pausemusic', 'Pausar música actual'),
            ('!resumemusic', 'Reanudar música'),
            ('!skipmusic', 'Saltar canción actual'),
            ('!stopmusic', 'Detener música'),
            ('!queuemusic', 'Ver cola de reproducción'),
            ('!volumemusic <nivel>', 'Cambiar volumen'),
            ('!nowplaying', 'Canción actual')
        ]
    },
    'xp': {
        'nombre': '⭐ XP y Niveles',
        'descripcion': 'Sistema de experiencia y niveles',
        'comandos': [
            ('!rank', 'Ver tu nivel y XP'),
            ('!top', 'Top 10 del servidor'),
            ('!addpoints <usuario> <cantidad>', 'Agregar XP (admin)'),
            ('!removepoints <usuario> <cantidad>', 'Quitar XP (admin)'),
            ('!resetxp <usuario>', 'Resetear XP (admin)'),
            ('!levelroles', 'Ver roles por nivel'),
            ('!setlevelrole <nivel> <rol>', 'Configurar rol (admin)'),
            ('!xpconfig', 'Configuración de XP (admin)')
        ]
    }
}

def test_problematic_scenarios():
    """Probar escenarios que podrían causar errores en paginación"""
    print('\n🔍 PRUEBAS DE ESCENARIOS PROBLEMÁTICOS')
    print('=' * 60)

    # Escenario 1: Categoría con muchos comandos largos
    categoria_problematica = {
        'nombre': '🤖 Inteligencia Artificial y Automatización Avanzada',
        'descripcion': 'Sistema completo de inteligencia artificial con múltiples funcionalidades avanzadas para automatización, procesamiento de lenguaje natural, generación de contenido, análisis de datos y mucho más',
        'comandos': [
            ('!chatgpt <mensaje muy largo>', 'Conversar con ChatGPT usando prompts personalizados y contexto avanzado para obtener respuestas detalladas'),
            ('!generateimage <descripción detallada>', 'Generar imágenes usando IA con descripciones muy específicas y parámetros avanzados'),
            ('!analyzetext <texto extenso>', 'Analizar texto completo incluyendo sentimientos, gramática, estilo y sugerencias de mejora'),
            ('!translateadvanced <idioma> <texto>', 'Traducción avanzada con contexto cultural y adaptación regional específica'),
            ('!summarizecomplex <documento>', 'Resumir documentos complejos manteniendo puntos clave y estructura original'),
            ('!codegeneration <lenguaje> <descripción>', 'Generar código completo en cualquier lenguaje con documentación incluida'),
            ('!dataanalysis <dataset>', 'Análisis completo de datos con visualizaciones y insights automáticos'),
            ('!contentcreation <tipo> <tema>', 'Crear contenido original para redes sociales, blogs y marketing digital'),
            ('!voiceprocessing <audio>', 'Procesamiento avanzado de audio con transcripción y análisis de sentimientos'),
            ('!automationworkflow <proceso>', 'Crear flujos de trabajo automatizados para tareas repetitivas complejas')
        ]
    }

    # Simular embed problemático
    title = categoria_problematica['nombre']
    description = categoria_problematica['descripcion'] + '\n\n**Haz clic en los botones para ejecutar comandos directamente**'

    fields = []
    comandos_principales = []

    # Usar todos los comandos sin límite
    for comando, descripcion_cmd in categoria_problematica['comandos']:
        comandos_principales.append(f'`{comando}` - {descripcion_cmd}')

    # Dividir en múltiples fields
    if len(comandos_principales) > 4:
        mid = len(comandos_principales) // 2
        fields.append(('🎯 Comandos (1/2)', '\n'.join(comandos_principales[:mid])))
        fields.append(('🎯 Comandos (2/2)', '\n'.join(comandos_principales[mid:])))

    # Agregar información adicional extensa
    fields.append(('🔧 Estado del Sistema Avanzado', '**IA Core:** ✅ Disponible\n**GPT Integration:** ✅ Activo\n**Image Generation:** ✅ Funcional\n**Voice Processing:** ✅ Operativo\n**Data Analysis:** ✅ Disponible\n**Automation Engine:** ✅ Activo'))
    fields.append(('💡 Tips Avanzados', 'Usa comandos específicos para mejores resultados\nCombina múltiples funciones para workflows complejos\nRevisa la documentación completa en el canal de ayuda'))
    fields.append(('🔄 Navegación Extendida', '**Página actual:** 1/3\n**Total categorías:** 45\n**Comandos en esta sección:** 10\nUsa **🔙 Volver** para regresar al menú principal'))
    fields.append(('📊 Estadísticas de Uso', '**Comandos ejecutados hoy:** 1,247\n**Usuarios activos:** 89\n**Tiempo promedio de respuesta:** 2.3s\n**Éxito rate:** 98.7%'))

    footer = '🎮 Haz clic en los botones para ejecutar comandos • Botón \'Volver\' para menú principal • Sistema de IA avanzado disponible 24/7'

    limites = check_embed_limits_simple(title, description, fields, footer)

    print(f'❌ ESCENARIO PROBLEMÁTICO: {limites["total_chars"]} caracteres')
    print(f'   ⚠️  EXCEDE LÍMITE: {limites["total_chars"] - 5500} caracteres de más')
    print(f'   📊 Fields: {limites["field_count"]}/25')

    # Mostrar desglose detallado
    print(f'\n📋 DESGLOSE DETALLADO:')
    print(f'   • Título: {len(title)} caracteres')
    print(f'   • Descripción: {len(description)} caracteres')
    print(f'   • Footer: {len(footer)} caracteres')

    for i, (field_name, field_value) in enumerate(fields):
        field_total = len(field_name) + len(field_value)
        print(f'   • Field {i+1}: {field_total} caracteres ({len(field_name)} + {len(field_value)})')

    print(f'\n🔧 SOLUCIONES RECOMENDADAS:')
    print(f'   1. Reducir descripción de comandos a máximo 30 caracteres')
    print(f'   2. Limitar a máximo 6 comandos por categoría')
    print(f'   3. Usar abreviaciones en nombres de fields')
    print(f'   4. Reducir información adicional')
    print(f'   5. Implementar sub-paginación para categorías grandes')

def main():
    print('📊 Verificación de Límites de Caracteres - Comando !com')
    print('=' * 60)

    for categoria_key, seccion in categorias.items():
        # Simular el embed que se crearía
        title = seccion['nombre']
        description = seccion['descripcion'] + '\n\n**Haz clic en los botones para ejecutar comandos directamente**'

        # Preparar fields
        fields = []

        # Comandos principales
        comandos_principales = []
        max_comandos = 8 if len(seccion['comandos']) > 6 else len(seccion['comandos'])

        for i, (comando, descripcion_cmd) in enumerate(seccion['comandos'][:max_comandos]):
            desc_corta = descripcion_cmd[:50] + '...' if len(descripcion_cmd) > 50 else descripcion_cmd
            comandos_principales.append(f'`{comando}` - {desc_corta}')

        if len(comandos_principales) > 4:
            mid = len(comandos_principales) // 2
            fields.append(('🎯 Comandos (1/2)', '\n'.join(comandos_principales[:mid])))
            fields.append(('🎯 Comandos (2/2)', '\n'.join(comandos_principales[mid:])))
        else:
            fields.append(('🎯 Comandos Principales', '\n'.join(comandos_principales)))

        # Información específica según la sección
        if categoria_key == 'gd':
            fields.append(('🔧 Estado del Sistema', '**gd.py:** ✅ Disponible\n**Fallback:** ✅ GDBrowser disponible\n**Búsquedas:** ✅ Funcional'))
            fields.append(('💡 Tip Rápido', 'Usa `!gduser RobTop` para buscar usuarios\no `!gdlevel 128` para ver niveles'))
        elif categoria_key == 'economia':
            fields.append(('💰 Tu Estado', 'Usa el botón **💰 Mi Balance** para ver tu dinero actual'))
            fields.append(('🎰 Casino', 'Juega **Blackjack** o **Crash** para ganar dinero'))
        elif categoria_key == 'musica':
            fields.append(('🎵 Estado Actual', 'Usa **🎵 Reproducir** para empezar a escuchar música'))
            fields.append(('💡 Tip', 'Puedes buscar por nombre o pegar URLs de YouTube'))
        elif categoria_key == 'xp':
            fields.append(('⭐ Tu Progreso', 'Usa **⭐ Mi Nivel** para ver tu progreso actual'))
            fields.append(('🏆 Competencia', 'Compite con otros usuarios del servidor'))

        # Navegación
        fields.append(('🔄 Navegación', '**Página actual:** 1/2\nUsa **🔙 Volver** para regresar al menú principal'))

        footer = '🎮 Haz clic en los botones para ejecutar comandos • Botón \'Volver\' para menú principal'

        # Verificar límites
        limites = check_embed_limits_simple(title, description, fields, footer)

        status = '✅' if limites['within_limits'] else '❌'
        print(f'{status} {seccion["nombre"]}: {limites["total_chars"]} caracteres')

        if not limites['within_limits']:
            print(f'   ⚠️  EXCEDE EL LÍMITE SEGURO (5500 caracteres)')
            print(f'   📊 Fields: {limites["field_count"]}/25')

    # Ejecutar pruebas de escenarios problemáticos
    test_problematic_scenarios()

    print()
    print('📏 Límites de Discord:')
    print('• Título: 256 caracteres')
    print('• Descripción: 4,096 caracteres')
    print('• Field Name: 256 caracteres')
    print('• Field Value: 1,024 caracteres')
    print('• Footer: 2,048 caracteres')
    print('• Total Embed: 6,000 caracteres (límite seguro: 5,500)')
    print()
    print('💡 Estado del Comando !com:')
    print('• ✅ Usa sistema de paginación para evitar límites')
    print('• ✅ Máximo 20 categorías por página')
    print('• ✅ Descripciones acortadas automáticamente')
    print('• ✅ Fields divididos cuando es necesario')
    print('• ✅ Límite seguro de 5,500 caracteres respetado')

if __name__ == '__main__':
    main()
