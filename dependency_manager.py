#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 Gestor de Dependencias Robusto - Paimon Bot
Maneja importaciones de manera segura y proporciona fallbacks
"""

import sys
import logging
import importlib
import subprocess
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass

@dataclass
class DependencyInfo:
    """Información sobre una dependencia"""
    name: str
    import_name: str
    version_check: Optional[str] = None
    install_name: Optional[str] = None
    required: bool = True
    fallback_available: bool = False
    description: str = ""

class DependencyManager:
    """Gestor robusto de dependencias"""
    
    def __init__(self):
        self.logger = logging.getLogger('DependencyManager')
        self.loaded_modules = {}
        self.failed_modules = {}
        self.dependency_status = {}
        
        # Definir dependencias del bot
        self.dependencies = {
            'discord': DependencyInfo(
                name='discord.py',
                import_name='discord',
                version_check='__version__',
                install_name='discord.py',
                required=True,
                description='Librería principal de Discord'
            ),
            'gd': DependencyInfo(
                name='gd.py',
                import_name='gd',
                version_check='__version__',
                install_name='gd.py==1.0.1',
                required=False,
                fallback_available=True,
                description='Librería para Geometry Dash'
            ),
            'aiohttp': DependencyInfo(
                name='aiohttp',
                import_name='aiohttp',
                version_check='__version__',
                install_name='aiohttp',
                required=True,
                description='Cliente HTTP asíncrono'
            ),
            'jwt': DependencyInfo(
                name='PyJWT',
                import_name='jwt',
                version_check='__version__',
                install_name='PyJWT',
                required=False,
                fallback_available=True,
                description='JSON Web Tokens para autenticación'
            ),
            'psutil': DependencyInfo(
                name='psutil',
                import_name='psutil',
                version_check='__version__',
                install_name='psutil',
                required=False,
                fallback_available=True,
                description='Información del sistema'
            ),
            'cryptography': DependencyInfo(
                name='cryptography',
                import_name='cryptography',
                version_check='__version__',
                install_name='cryptography',
                required=False,
                fallback_available=True,
                description='Funciones criptográficas'
            ),
            'beautifulsoup4': DependencyInfo(
                name='BeautifulSoup4',
                import_name='bs4',
                version_check=None,
                install_name='beautifulsoup4',
                required=False,
                fallback_available=True,
                description='Parser HTML/XML'
            )
        }
        
        self.logger.info("📦 DependencyManager inicializado")
    
    def safe_import(self, dependency_key: str) -> Tuple[bool, Optional[Any], str]:
        """Importa una dependencia de manera segura"""
        try:
            if dependency_key not in self.dependencies:
                return False, None, f"Dependencia '{dependency_key}' no definida"
            
            dep_info = self.dependencies[dependency_key]
            
            # Verificar si ya está cargada
            if dependency_key in self.loaded_modules:
                return True, self.loaded_modules[dependency_key], "Ya cargada"
            
            # Verificar si ya falló anteriormente
            if dependency_key in self.failed_modules:
                return False, None, self.failed_modules[dependency_key]
            
            # Intentar importar
            try:
                module = importlib.import_module(dep_info.import_name)
                
                # Verificar versión si está disponible
                version_info = ""
                if dep_info.version_check:
                    try:
                        version = getattr(module, dep_info.version_check, 'unknown')
                        version_info = f" (v{version})"
                    except:
                        version_info = " (versión desconocida)"
                
                # Guardar módulo cargado
                self.loaded_modules[dependency_key] = module
                self.dependency_status[dependency_key] = {
                    'status': 'loaded',
                    'version': version_info,
                    'module': module
                }
                
                success_msg = f"✅ {dep_info.name} cargada{version_info}"
                self.logger.info(success_msg)
                return True, module, success_msg
                
            except ImportError as e:
                error_msg = f"❌ No se pudo importar {dep_info.name}: {str(e)}"
                
                # Guardar error
                self.failed_modules[dependency_key] = error_msg
                self.dependency_status[dependency_key] = {
                    'status': 'failed',
                    'error': str(e),
                    'fallback_available': dep_info.fallback_available
                }
                
                self.logger.warning(error_msg)
                
                # Si no es requerida, no es un error crítico
                if not dep_info.required:
                    self.logger.info(f"⚠️ {dep_info.name} es opcional, continuando sin ella")
                
                return False, None, error_msg
                
        except Exception as e:
            error_msg = f"❌ Error inesperado importando {dependency_key}: {str(e)}"
            self.logger.error(error_msg)
            return False, None, error_msg
    
    def get_module(self, dependency_key: str) -> Optional[Any]:
        """Obtiene un módulo cargado"""
        return self.loaded_modules.get(dependency_key)
    
    def is_available(self, dependency_key: str) -> bool:
        """Verifica si una dependencia está disponible"""
        return dependency_key in self.loaded_modules
    
    def install_dependency(self, dependency_key: str) -> Tuple[bool, str]:
        """Intenta instalar una dependencia faltante"""
        try:
            if dependency_key not in self.dependencies:
                return False, f"Dependencia '{dependency_key}' no definida"
            
            dep_info = self.dependencies[dependency_key]
            install_name = dep_info.install_name or dep_info.import_name
            
            self.logger.info(f"📦 Intentando instalar {dep_info.name}...")
            
            # Ejecutar pip install
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', install_name],
                capture_output=True,
                text=True,
                timeout=300  # 5 minutos timeout
            )
            
            if result.returncode == 0:
                # Limpiar caché de módulos fallidos
                if dependency_key in self.failed_modules:
                    del self.failed_modules[dependency_key]
                
                # Intentar importar de nuevo
                success, module, msg = self.safe_import(dependency_key)
                if success:
                    return True, f"✅ {dep_info.name} instalada e importada correctamente"
                else:
                    return False, f"❌ Instalada pero no se pudo importar: {msg}"
            else:
                error_msg = f"❌ Error instalando {dep_info.name}: {result.stderr}"
                self.logger.error(error_msg)
                return False, error_msg
                
        except subprocess.TimeoutExpired:
            return False, f"❌ Timeout instalando {dep_info.name}"
        except Exception as e:
            error_msg = f"❌ Error inesperado instalando {dependency_key}: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def load_all_dependencies(self) -> Dict[str, Dict[str, Any]]:
        """Carga todas las dependencias definidas"""
        results = {}
        
        self.logger.info("📦 Cargando todas las dependencias...")
        
        for dep_key, dep_info in self.dependencies.items():
            success, module, message = self.safe_import(dep_key)
            results[dep_key] = {
                'success': success,
                'module': module,
                'message': message,
                'required': dep_info.required,
                'fallback_available': dep_info.fallback_available
            }
        
        # Verificar dependencias críticas
        critical_failures = [
            dep_key for dep_key, result in results.items()
            if not result['success'] and self.dependencies[dep_key].required
        ]
        
        if critical_failures:
            self.logger.error(f"❌ Dependencias críticas faltantes: {critical_failures}")
        else:
            self.logger.info("✅ Todas las dependencias críticas cargadas")
        
        return results
    
    def get_dependency_report(self) -> Dict[str, Any]:
        """Genera un reporte completo de dependencias"""
        report = {
            'loaded': {},
            'failed': {},
            'optional_missing': {},
            'critical_missing': {},
            'total_dependencies': len(self.dependencies),
            'loaded_count': len(self.loaded_modules),
            'failed_count': len(self.failed_modules)
        }
        
        for dep_key, dep_info in self.dependencies.items():
            status = self.dependency_status.get(dep_key, {'status': 'not_checked'})
            
            if dep_key in self.loaded_modules:
                report['loaded'][dep_key] = {
                    'name': dep_info.name,
                    'version': status.get('version', 'unknown'),
                    'description': dep_info.description
                }
            elif dep_key in self.failed_modules:
                if dep_info.required:
                    report['critical_missing'][dep_key] = {
                        'name': dep_info.name,
                        'error': status.get('error', 'unknown'),
                        'description': dep_info.description
                    }
                else:
                    report['optional_missing'][dep_key] = {
                        'name': dep_info.name,
                        'error': status.get('error', 'unknown'),
                        'fallback_available': dep_info.fallback_available,
                        'description': dep_info.description
                    }
        
        return report
    
    def create_fallback_module(self, dependency_key: str) -> Optional[Any]:
        """Crea un módulo fallback para dependencias opcionales"""
        try:
            if dependency_key == 'gd':
                return self._create_gd_fallback()
            elif dependency_key == 'jwt':
                return self._create_jwt_fallback()
            elif dependency_key == 'psutil':
                return self._create_psutil_fallback()
            elif dependency_key == 'cryptography':
                return self._create_crypto_fallback()
            elif dependency_key == 'beautifulsoup4':
                return self._create_bs4_fallback()
            else:
                return None
        except Exception as e:
            self.logger.error(f"❌ Error creando fallback para {dependency_key}: {e}")
            return None
    
    def _create_gd_fallback(self):
        """Crea fallback para gd.py"""
        class GDFallback:
            def __init__(self):
                self.__version__ = "fallback-1.0.0"
            
            def get_level(self, level_id):
                raise NotImplementedError("gd.py no está disponible")
            
            def search_levels(self, query):
                raise NotImplementedError("gd.py no está disponible")
        
        return GDFallback()
    
    def _create_jwt_fallback(self):
        """Crea fallback para JWT"""
        class JWTFallback:
            def __init__(self):
                self.__version__ = "fallback-1.0.0"
            
            def encode(self, payload, secret, algorithm='HS256'):
                raise NotImplementedError("PyJWT no está disponible")
            
            def decode(self, token, secret, algorithms=None):
                raise NotImplementedError("PyJWT no está disponible")
        
        return JWTFallback()
    
    def _create_psutil_fallback(self):
        """Crea fallback para psutil"""
        class PSUtilFallback:
            def __init__(self):
                self.__version__ = "fallback-1.0.0"
            
            def virtual_memory(self):
                class MemoryInfo:
                    percent = 0.0
                return MemoryInfo()
        
        return PSUtilFallback()
    
    def _create_crypto_fallback(self):
        """Crea fallback para cryptography"""
        class CryptoFallback:
            def __init__(self):
                self.__version__ = "fallback-1.0.0"
        
        return CryptoFallback()
    
    def _create_bs4_fallback(self):
        """Crea fallback para BeautifulSoup"""
        class BS4Fallback:
            def __init__(self):
                pass
            
            def BeautifulSoup(self, markup, parser='html.parser'):
                raise NotImplementedError("BeautifulSoup4 no está disponible")
        
        return BS4Fallback()

# Instancia global del gestor
_dependency_manager = None

def get_dependency_manager() -> DependencyManager:
    """Obtiene la instancia global del gestor de dependencias"""
    global _dependency_manager
    if _dependency_manager is None:
        _dependency_manager = DependencyManager()
    return _dependency_manager

def safe_import(dependency_key: str) -> Tuple[bool, Optional[Any], str]:
    """Función de conveniencia para importación segura"""
    return get_dependency_manager().safe_import(dependency_key)

def get_module(dependency_key: str) -> Optional[Any]:
    """Función de conveniencia para obtener módulo"""
    return get_dependency_manager().get_module(dependency_key)

def is_available(dependency_key: str) -> bool:
    """Función de conveniencia para verificar disponibilidad"""
    return get_dependency_manager().is_available(dependency_key)
