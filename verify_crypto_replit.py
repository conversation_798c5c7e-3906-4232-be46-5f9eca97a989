#!/usr/bin/env python3
"""
Verificador del Sistema de Cifrado para Replit - Paimon Bot
Verifica que todo esté configurado correctamente en Replit
"""

import os
import sys
import importlib

def check_environment():
    """Verificar entorno de Replit"""
    print("🌐 Verificando entorno de Replit...")

    replit_indicators = {
        'REPL_ID': os.getenv('REPL_ID'),
        'REPLIT_DB_URL': os.getenv('REPLIT_DB_URL'),
        'REPL_SLUG': os.getenv('REPL_SLUG'),
        'REPL_OWNER': os.getenv('REPL_OWNER'),
        'REPL_ENVIRONMENT': os.getenv('REPL_ENVIRONMENT')
    }

    replit_detected = any(replit_indicators.values())

    if replit_detected:
        print("✅ Entorno Replit detectado correctamente")
        for key, value in replit_indicators.items():
            if value:
                print(f"   • {key}: {value}")
    else:
        print("⚠️ No se detectó entorno Replit")

    return replit_detected

def check_dependencies():
    """Verificar dependencias de cifrado"""
    print("\n🔐 Verificando dependencias de cifrado...")

    dependencies = [
        ("cryptography", "cryptography"),
        ("pycryptodome", "Crypto"),
        ("flask", "flask")
    ]

    all_installed = True

    for package, import_name in dependencies:
        try:
            importlib.import_module(import_name)
            print(f"✅ {package} - Instalado y funcionando")
        except ImportError:
            print(f"❌ {package} - No encontrado")
            all_installed = False

    return all_installed

def check_directories():
    """Verificar directorios necesarios"""
    print("\n📁 Verificando estructura de directorios...")

    directories = ["data", "src"]
    all_exist = True

    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}/ - Existe")
        else:
            print(f"❌ {directory}/ - No encontrado")
            all_exist = False

    return all_exist

def check_files():
    """Verificar archivos necesarios"""
    print("\n📄 Verificando archivos del sistema...")

    files = [
        "main.py" if os.path.exists("main.py") else "Bot.py",
        "src/crypto_manager.py",
        "pyproject.toml",
        ".replit"
    ]

    all_exist = True

    for file in files:
        if os.path.exists(file):
            print(f"✅ {file} - Existe")
        else:
            print(f"❌ {file} - No encontrado")
            all_exist = False

    return all_exist

def check_crypto_system():
    """Verificar que el sistema de cifrado funcione"""
    print("\n🔐 Probando sistema de cifrado...")

    try:
        # Importar el gestor de cifrado
        sys.path.append('src')
        from crypto_manager import crypto_manager

        print("✅ crypto_manager - Importado correctamente")

        # Verificar métodos principales
        methods = ['user_exists', 'encrypt_user_data', 'decrypt_user_data', 'get_user_stats']

        for method in methods:
            if hasattr(crypto_manager, method):
                print(f"✅ {method} - Método disponible")
            else:
                print(f"❌ {method} - Método no encontrado")
                return False

        # Probar estadísticas
        stats = crypto_manager.get_user_stats()
        print(f"✅ Estadísticas obtenidas: {stats['total_users']} usuarios")

        return True

    except Exception as e:
        print(f"❌ Error en sistema de cifrado: {e}")
        return False

def check_bot_integration():
    """Verificar integración con el bot"""
    print("\n🤖 Verificando integración con el bot...")

    try:
        # Verificar que main.py o Bot.py tenga las importaciones necesarias
        bot_file = 'main.py' if os.path.exists('main.py') else 'Bot.py'
        with open(bot_file, 'r', encoding='utf-8') as f:
            bot_content = f.read()

        checks = [
            ('crypto_manager', 'from src.crypto_manager import crypto_manager'),
            ('CRYPTO_AVAILABLE', 'CRYPTO_AVAILABLE = True'),
            ('crypto_commands', 'from modules.crypto_commands import setup_crypto_commands'),
        ]

        for check_name, check_string in checks:
            if check_string in bot_content:
                print(f"✅ {check_name} - Integrado correctamente")
            else:
                print(f"❌ {check_name} - No encontrado en {bot_file}")
                return False

        return True

    except Exception as e:
        print(f"❌ Error verificando Bot.py: {e}")
        return False

def show_replit_status():
    """Mostrar estado completo para Replit"""
    print("\n" + "=" * 70)
    print("📊 RESUMEN DEL ESTADO DEL SISTEMA DE CIFRADO EN REPLIT")
    print("=" * 70)

    # Ejecutar todas las verificaciones
    env_ok = check_environment()
    deps_ok = check_dependencies()
    dirs_ok = check_directories()
    files_ok = check_files()
    crypto_ok = check_crypto_system()
    bot_ok = check_bot_integration()

    # Resumen final
    print("\n📋 RESUMEN FINAL:")
    print("=" * 30)

    checks = [
        ("Entorno Replit", env_ok),
        ("Dependencias", deps_ok),
        ("Directorios", dirs_ok),
        ("Archivos", files_ok),
        ("Sistema de Cifrado", crypto_ok),
        ("Integración Bot", bot_ok)
    ]

    all_ok = True
    for check_name, status in checks:
        icon = "✅" if status else "❌"
        print(f"{icon} {check_name}: {'OK' if status else 'FALLO'}")
        if not status:
            all_ok = False

    print("\n" + "=" * 70)

    if all_ok:
        print("🎉 ¡SISTEMA COMPLETAMENTE FUNCIONAL EN REPLIT!")
        print("✅ El sistema de cifrado está listo para usar")
        print("🚀 Puedes ejecutar el bot con: python Bot.py")

        print("\n🎯 Comandos disponibles:")
        print("• !register <usuario> <contraseña> - Registrar cuenta")
        print("• !login <contraseña> - Iniciar sesión")
        print("• !account - Ver estado de cuenta")
        print("• !com → 👤 Cuenta - Interfaz gráfica")

    else:
        print("⚠️ SISTEMA INCOMPLETO - REQUIERE CONFIGURACIÓN")
        print("🔧 Ejecuta: python install_crypto_replit.py")
        print("📖 Lee: REPLIT_CRYPTO_SETUP.md para más detalles")

    return all_ok

def main():
    print("🔐 Verificador del Sistema de Cifrado para Replit")
    print("=" * 60)

    try:
        status = show_replit_status()

        if status:
            print("\n💡 Próximos pasos:")
            print("1. Ejecuta el bot: python Bot.py")
            print("2. Prueba !com en Discord")
            print("3. Registra una cuenta de prueba")
            print("4. Configura backup en GitHub (opcional)")
        else:
            print("\n🔧 Pasos para solucionar:")
            print("1. python install_crypto_replit.py")
            print("2. poetry install")
            print("3. Reinicia el Repl")
            print("4. python verify_crypto_replit.py")

    except Exception as e:
        print(f"\n❌ Error durante la verificación: {e}")
        print("🔧 Intenta reinstalar: python install_crypto_replit.py")

if __name__ == "__main__":
    main()
