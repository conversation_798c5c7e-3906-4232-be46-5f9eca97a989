#!/usr/bin/env python3
"""
Configurador Específico para el Repositorio de Fl0zWer
Configura GitHub para guardar datos en https://github.com/Fl0zWer/Paimon/tree/main/data
"""

import os

def setup_fl0zwer_config():
    """Configurar variables específicas para el repositorio de Fl0zWer"""
    print("🌐 Configurador para Repositorio Fl0zWer/Paimon")
    print("=" * 50)
    
    # Configuración específica
    config = {
        'GITHUB_REPO_OWNER': 'Fl0zWer',
        'GITHUB_REPO_NAME': 'Paimon',
        'GITHUB_BRANCH': 'main',
        'GITHUB_FOLDER': 'data'
    }
    
    print("📋 Configuración del repositorio:")
    print(f"• Repositorio: https://github.com/{config['GITHUB_REPO_OWNER']}/{config['GITHUB_REPO_NAME']}")
    print(f"• Rama: {config['GITHUB_BRANCH']}")
    print(f"• Carpeta de datos: {config['GITHUB_FOLDER']}/")
    print(f"• URL de datos: https://github.com/{config['GITHUB_REPO_OWNER']}/{config['GITHUB_REPO_NAME']}/tree/{config['GITHUB_BRANCH']}/{config['GITHUB_FOLDER']}")
    
    # Verificar token
    github_token = os.getenv('GITHUB_TOKEN')
    
    if github_token:
        print(f"\n✅ GITHUB_TOKEN configurado: {github_token[:8]}...{github_token[-4:]}")
    else:
        print("\n❌ GITHUB_TOKEN no configurado")
        print("\n📝 Para configurar el token:")
        print("1. Ve a: https://github.com/settings/tokens")
        print("2. Click 'Generate new token (classic)'")
        print("3. Selecciona permisos: 'repo' y 'public_repo'")
        print("4. Copia el token")
        print("5. En Replit, ve a 'Secrets' y agrega:")
        print("   GITHUB_TOKEN=tu_token_aqui")
        return False
    
    return True

def test_fl0zwer_connection():
    """Probar conexión específica con el repositorio Fl0zWer/Paimon"""
    print("\n🔗 Probando conexión con Fl0zWer/Paimon...")
    
    try:
        import requests
        
        github_token = os.getenv('GITHUB_TOKEN')
        
        if not github_token:
            print("❌ Token no configurado")
            return False
        
        # Probar acceso al repositorio específico
        url = "https://api.github.com/repos/Fl0zWer/Paimon"
        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            repo_data = response.json()
            print(f"✅ Conexión exitosa con {repo_data['full_name']}")
            print(f"   • Descripción: {repo_data.get('description', 'Sin descripción')}")
            print(f"   • Privado: {'Sí' if repo_data['private'] else 'No'}")
            print(f"   • Rama por defecto: {repo_data['default_branch']}")
            
            # Verificar carpeta data/
            data_url = "https://api.github.com/repos/Fl0zWer/Paimon/contents/data"
            data_response = requests.get(data_url, headers=headers)
            
            if data_response.status_code == 200:
                print("✅ Carpeta data/ encontrada")
                data_contents = data_response.json()
                if isinstance(data_contents, list):
                    print(f"   • Archivos existentes: {len(data_contents)}")
                    for item in data_contents[:3]:  # Mostrar solo los primeros 3
                        print(f"     - {item['name']}")
                    if len(data_contents) > 3:
                        print(f"     - ... y {len(data_contents) - 3} más")
            else:
                print("ℹ️ Carpeta data/ no existe (se creará automáticamente)")
            
            return True
            
        elif response.status_code == 404:
            print("❌ Repositorio Fl0zWer/Paimon no encontrado o sin acceso")
            print("   Verifica que el token tenga permisos para este repositorio")
            return False
        elif response.status_code == 401:
            print("❌ Token de GitHub inválido")
            return False
        else:
            print(f"❌ Error de conexión: {response.status_code}")
            return False
            
    except ImportError:
        print("❌ Módulo 'requests' no disponible")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_replit_setup():
    """Mostrar instrucciones específicas para Replit"""
    print("\n📝 CONFIGURACIÓN EN REPLIT:")
    print("=" * 40)
    print("1. Ve a la pestaña 'Secrets' en Replit")
    print("2. Agrega exactamente estas variables:")
    print("")
    print("   GITHUB_TOKEN=tu_token_personal")
    print("   GITHUB_REPO_OWNER=Fl0zWer")
    print("   GITHUB_REPO_NAME=Paimon")
    print("   GITHUB_BRANCH=main")
    print("")
    print("3. Reinicia el Repl")
    print("4. Ejecuta: python setup_fl0zwer_github.py")

def create_test_file():
    """Crear archivo de prueba en GitHub"""
    print("\n🧪 Creando archivo de prueba...")
    
    try:
        import requests
        import base64
        from datetime import datetime
        
        github_token = os.getenv('GITHUB_TOKEN')
        
        if not github_token:
            print("❌ Token no configurado")
            return False
        
        # Contenido de prueba
        test_content = f"""# Test File - Paimon Bot Crypto System

This is a test file created on {datetime.now().isoformat()}

The crypto system is working correctly and can write to GitHub!

Repository: https://github.com/Fl0zWer/Paimon
Folder: data/
System: AES-256-GCM Encryption
"""
        
        content_b64 = base64.b64encode(test_content.encode('utf-8')).decode('utf-8')
        
        url = "https://api.github.com/repos/Fl0zWer/Paimon/contents/data/crypto_test.md"
        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'message': 'Test crypto system - Paimon Bot',
            'content': content_b64,
            'branch': 'main'
        }
        
        response = requests.put(url, headers=headers, json=payload)
        
        if response.status_code in [200, 201]:
            print("✅ Archivo de prueba creado exitosamente")
            print("   Ver en: https://github.com/Fl0zWer/Paimon/blob/main/data/crypto_test.md")
            return True
        else:
            print(f"❌ Error creando archivo de prueba: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 Configurador para Fl0zWer/Paimon - Sistema de Cifrado")
    print("=" * 60)
    
    # Configurar variables específicas
    if not setup_fl0zwer_config():
        show_replit_setup()
        return
    
    # Probar conexión
    if test_fl0zwer_connection():
        print("\n🎉 ¡Configuración exitosa!")
        
        # Crear archivo de prueba
        if create_test_file():
            print("\n✅ Sistema completamente configurado")
            print("\n🚀 Próximos pasos:")
            print("1. Ejecuta tu bot: python main.py")
            print("2. Registra una cuenta de prueba: !register TestUser TestPass123")
            print("3. Verifica los datos en: https://github.com/Fl0zWer/Paimon/tree/main/data")
            print("\n📁 Los archivos .Paimon aparecerán en:")
            print("   https://github.com/Fl0zWer/Paimon/tree/main/data")
        else:
            print("\n⚠️ Configuración parcial - verifica permisos del token")
    else:
        print("\n❌ Error de configuración")
        show_replit_setup()

if __name__ == "__main__":
    main()
