#!/usr/bin/env python3
"""
Script de prueba para verificar las APIs de TTS
"""

import os
import sys
import tempfile
import requests
import json
import base64

# Simular las variables globales del bot
TTS_API_KEYS = {
    'google_cloud': None,
    'elevenlabs': None,
    'azure': None
}

def text_to_speech_google_cloud(text, language='es', speed=1.0, voice_name='es-ES-Neural2-A'):
    """Convert text to speech using Google Cloud TTS API"""
    try:
        # Google Cloud TTS API endpoint
        url = "https://texttospeech.googleapis.com/v1/text:synthesize"

        # API Key from stored keys
        api_key = TTS_API_KEYS.get('google_cloud') or os.getenv('GOOGLE_CLOUD_API_KEY')
        if not api_key:
            print("[DEBUG] Google Cloud API key not found")
            return None

        # Prepare the request
        data = {
            "input": {"text": text},
            "voice": {
                "languageCode": language,
                "name": voice_name
            },
            "audioConfig": {
                "audioEncoding": "MP3",
                "speakingRate": speed,
                "volumeGainDb": 0
            }
        }

        # Make the request
        response = requests.post(
            f"{url}?key={api_key}",
            json=data,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            # Decode the audio content
            audio_content = response.json()['audioContent']
            audio_data = base64.b64decode(audio_content)

            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as fp:
                temp_filename = fp.name
                fp.write(audio_data)

            print(f"[DEBUG] Google Cloud TTS generado en: {temp_filename}")
            return temp_filename
        else:
            print(f"[DEBUG] Google Cloud TTS error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"[DEBUG] Error en Google Cloud TTS: {e}")
        return None

def text_to_speech_elevenlabs(text, voice_id='21m00Tcm4TlvDq8ikWAM', speed=1.0):
    """Convert text to speech using ElevenLabs API"""
    try:
        # ElevenLabs API endpoint
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

        # API Key from stored keys
        api_key = TTS_API_KEYS.get('elevenlabs') or os.getenv('ELEVENLABS_API_KEY')
        if not api_key:
            print("[DEBUG] ElevenLabs API key not found")
            return None

        # Prepare the request
        data = {
            "text": text,
            "model_id": "eleven_multilingual_v2",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }

        # Make the request
        response = requests.post(
            url,
            json=data,
            headers={
                'Accept': 'audio/mpeg',
                'xi-api-key': api_key,
                'Content-Type': 'application/json'
            }
        )

        if response.status_code == 200:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as fp:
                temp_filename = fp.name
                fp.write(response.content)

            print(f"[DEBUG] ElevenLabs TTS generado en: {temp_filename}")
            return temp_filename
        else:
            print(f"[DEBUG] ElevenLabs TTS error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"[DEBUG] Error en ElevenLabs TTS: {e}")
        return None

def text_to_speech_azure(text, language='es-ES', voice_name='es-ES-ElviraNeural', speed=1.0):
    """Convert text to speech using Azure Cognitive Services"""
    try:
        # Azure TTS API endpoint
        subscription_key = TTS_API_KEYS.get('azure') or os.getenv('AZURE_SPEECH_KEY')
        region = os.getenv('AZURE_SPEECH_REGION', 'eastus')

        if not subscription_key:
            print("[DEBUG] Azure Speech API key not found")
            return None

        url = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"

        # Prepare the request
        headers = {
            'Ocp-Apim-Subscription-Key': subscription_key,
            'Content-Type': 'application/ssml+xml',
            'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3'
        }

        # Create SSML
        ssml = f"""
        <speak version='1.0' xml:lang='{language}'>
            <voice xml:lang='{language}' xml:gender='Female' name='{voice_name}'>
                <prosody rate='{speed}'>
                    {text}
                </prosody>
            </voice>
        </speak>
        """

        # Make the request
        response = requests.post(url, headers=headers, data=ssml.encode('utf-8'))

        if response.status_code == 200:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as fp:
                temp_filename = fp.name
                fp.write(response.content)

            print(f"[DEBUG] Azure TTS generado en: {temp_filename}")
            return temp_filename
        else:
            print(f"[DEBUG] Azure TTS error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"[DEBUG] Error en Azure TTS: {e}")
        return None

def test_api(api_name, api_key):
    """Test a specific API"""
    print(f"\n🧪 Probando API: {api_name}")
    print(f"API Key: {api_key[:10]}..." if api_key else "No API key")

    # Set API key
    if api_key:
        TTS_API_KEYS[api_name] = api_key
        os.environ[f"{api_name.upper()}_API_KEY"] = api_key

    test_text = "Hola, esto es una prueba de la API de TTS."

    try:
        if api_name == "google_cloud":
            audio_file = text_to_speech_google_cloud(test_text)
        elif api_name == "elevenlabs":
            audio_file = text_to_speech_elevenlabs(test_text)
        elif api_name == "azure":
            audio_file = text_to_speech_azure(test_text)
        else:
            print(f"❌ API '{api_name}' no es válida")
            return False

        if audio_file:
            print(f"✅ API {api_name} funciona correctamente")
            # Clean up test file
            try:
                if os.path.exists(audio_file):
                    os.remove(audio_file)
            except:
                pass
            return True
        else:
            print(f"❌ API {api_name} no pudo generar audio")
            return False

    except Exception as e:
        print(f"❌ Error probando API {api_name}: {str(e)}")
        return False

def main():
    print("🎤 Prueba de APIs de TTS")
    print("=" * 50)

    # Check environment variables
    print("\n📋 Variables de entorno:")
    google_key = os.getenv('GOOGLE_CLOUD_API_KEY')
    elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
    azure_key = os.getenv('AZURE_SPEECH_KEY')

    print(f"GOOGLE_CLOUD_API_KEY: {'✅ Configurada' if google_key else '❌ No configurada'}")
    print(f"ELEVENLABS_API_KEY: {'✅ Configurada' if elevenlabs_key else '❌ No configurada'}")
    print(f"AZURE_SPEECH_KEY: {'✅ Configurada' if azure_key else '❌ No configurada'}")

    # Test APIs
    if google_key:
        test_api("google_cloud", google_key)

    if elevenlabs_key:
        test_api("elevenlabs", elevenlabs_key)

    if azure_key:
        test_api("azure", azure_key)

    print("\n" + "=" * 50)
    print("✅ Prueba completada")

if __name__ == "__main__":
    main() 