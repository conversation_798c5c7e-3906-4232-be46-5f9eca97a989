"""
Keep Alive - Mantiene el bot activo en Replit
Usa Flask para crear un servidor web que mantiene el bot funcionando
Incluye endpoints adicionales para monitoreo y estadísticas
"""

from flask import Flask, jsonify
from threading import Thread
import os
import time
import psutil
import discord

app = Flask('')

# Variables globales para estadísticas
start_time = time.time()
request_count = 0

@app.route('/')
def home():
    global request_count
    request_count += 1
    uptime = time.time() - start_time
    hours = int(uptime // 3600)
    minutes = int((uptime % 3600) // 60)

    return f"""
    <html>
    <head>
        <title>Paimon Bot - Status</title>
        <meta http-equiv="refresh" content="30">
        <style>
            body {{ font-family: Arial, sans-serif; background: #2c2f33; color: #ffffff; text-align: center; padding: 50px; }}
            .container {{ max-width: 600px; margin: 0 auto; background: #36393f; padding: 30px; border-radius: 10px; }}
            .status {{ color: #57f287; font-size: 24px; margin: 20px 0; }}
            .info {{ background: #40444b; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 Paimon Bot Helper</h1>
            <div class="status">✅ ACTIVO Y FUNCIONANDO</div>
            <div class="info">
                <strong>⏰ Tiempo Activo:</strong> {hours}h {minutes}m<br>
                <strong>📊 Requests:</strong> {request_count}<br>
                <strong>🔄 Auto-refresh:</strong> Cada 30 segundos
            </div>
            <p>Este servidor mantiene el bot activo en Replit 24/7</p>
        </div>
    </body>
    </html>
    """

@app.route('/status')
def status():
    """Endpoint de estado en JSON"""
    global request_count
    request_count += 1
    uptime = time.time() - start_time

    try:
        # Obtener uso de memoria
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent()
    except:
        memory_usage = 0
        cpu_usage = 0

    return jsonify({
        'status': 'online',
        'uptime_seconds': int(uptime),
        'uptime_formatted': f"{int(uptime // 3600)}h {int((uptime % 3600) // 60)}m",
        'requests': request_count,
        'memory_usage': f"{memory_usage}%",
        'cpu_usage': f"{cpu_usage}%",
        'timestamp': int(time.time())
    })

@app.route('/ping')
def ping():
    """Endpoint simple para ping"""
    global request_count
    request_count += 1
    return "pong"

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': int(time.time()),
        'service': 'paimon-bot'
    })

def run():
    """Ejecuta el servidor Flask"""
    port = int(os.environ.get('PORT', 4000))
    app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)

def keep_alive():
    """Inicia el servidor en un hilo separado"""
    server = Thread(target=run)
    server.daemon = True
    server.start()
    print("🌐 Servidor web iniciado para mantener el bot activo")
    print(f"📡 Disponible en: https://{os.environ.get('REPL_SLUG', 'tu-repl')}.{os.environ.get('REPL_OWNER', 'usuario')}.repl.co")
