#!/usr/bin/env python3
"""
Script para probar los límites de caracteres de las categorías REALES del comando !com
"""

def check_embed_limits_simple(title, description, fields, footer):
    """Verificar límites de caracteres de un embed"""
    total_chars = 0
    
    if title:
        total_chars += len(title)
    if description:
        total_chars += len(description)
    
    for field_name, field_value in fields:
        total_chars += len(field_name) + len(field_value)
    
    if footer:
        total_chars += len(footer)
    
    return {
        'total_chars': total_chars,
        'within_limits': total_chars <= 5500,
        'field_count': len(fields)
    }

# Categorías REALES extraídas del main.py
categorias_reales = {
    'counter': {
        'nombre': '🎯 Paimon Counter Pro',
        'descripcion': 'Sistema de conteo avanzado con múltiples modos y estadísticas',
        'comandos': [
            ('!setupcounter [modo] [tema]', 'Configurar canal de conteo'),
            ('!counterstats [usuario]', 'Ver estadísticas detalladas de conteo'),
            ('!countertop [cantidad]', 'Ranking de mejores contadores'),
            ('!counterhelp', 'Ayuda completa del sistema'),
            ('!counterevent [tipo]', 'Activar evento especial de conteo'),
            ('!counterreset', 'Reiniciar contador del canal (admin)'),
            ('!counterpb [usuario]', 'Ver récord personal de conteo'),
            ('!counterstreak [usuario]', 'Ver racha actual de conteo'),
            ('!countermodes', 'Ver todos los modos disponibles'),
            ('!counterleaderboard', 'Tabla de líderes global'),
            ('!countermilestones', 'Ver hitos y logros de conteo'),
            ('!counternotify', 'Configurar notificaciones de hitos')
        ]
    },
    'tts': {
        'nombre': '🎤 Text-to-Speech Pro',
        'descripcion': 'Sistema avanzado de texto a voz con múltiples voces',
        'comandos': [
            ('!paimontts', 'Activar TTS de Paimon'),
            ('!stoptts', 'Detener TTS'),
            ('!ttsstatus', 'Ver estado del TTS'),
            ('!tts <mensaje>', 'Leer mensaje específico'),
            ('!say <texto>', 'Decir texto específico'),
            ('!ttsvoice <voz>', 'Cambiar voz del TTS'),
            ('!ttsvoices', 'Ver voces disponibles'),
            ('!ttsspeed <velocidad>', 'Cambiar velocidad de lectura'),
            ('!ttsvolume <volumen>', 'Cambiar volumen del TTS'),
            ('!ttslang <idioma>', 'Cambiar idioma del TTS'),
            ('!ttsqueue', 'Ver cola de mensajes TTS'),
            ('!ttsclear', 'Limpiar cola de TTS')
        ]
    },
    'economia': {
        'nombre': '💰 Economía',
        'descripcion': 'Sistema económico completo con tienda y casino',
        'comandos': [
            ('!balance [usuario]', 'Ver dinero del usuario'),
            ('!daily', 'Recompensa diaria'),
            ('!work', 'Trabajar para ganar dinero'),
            ('!shop', 'Ver tienda con precios dinámicos'),
            ('!buy <item> [cantidad]', 'Comprar item de la tienda'),
            ('!inventory [usuario]', 'Ver inventario'),
            ('!marketplace', 'Mercado entre usuarios'),
            ('!casino', 'Acceder al casino principal'),
            ('!blackjack <apuesta>', 'Jugar Blackjack'),
            ('!crash <apuesta>', 'Jugar Crash/Aviator'),
            ('!casinodaily', 'Bono diario VIP del casino'),
            ('!casinomissions', 'Ver misiones del casino'),
            ('!casinobadges', 'Ver badges del casino')
        ]
    },
    'reactions': {
        'nombre': '😊 Reacciones y Emociones',
        'descripcion': 'Expresa tus emociones con GIFs',
        'comandos': [
            ('!react', 'Ver todas las reacciones disponibles'),
            ('!react happy', 'Expresar felicidad'),
            ('!react sad', 'Expresar tristeza'),
            ('!react angry', 'Expresar enojo'),
            ('!react confused', 'Expresar confusión'),
            ('!react blush', 'Sonrojarse'),
            ('!react scared', 'Expresar miedo'),
            ('!react smile', 'Sonreír'),
            ('!react bored', 'Expresar aburrimiento'),
            ('!react shrug', 'Encogerse de hombros'),
            ('!react thinking', 'Pensar profundamente'),
            ('!react scream', 'Gritar de emoción'),
            ('!react teehee', 'Risita traviesa'),
            ('!react nope', 'Negarse rotundamente'),
            ('!react peek', 'Observar sigilosamente'),
            ('!react wasted', 'Estar derrotado')
        ]
    },
    'actions': {
        'nombre': '🎭 Acciones y Roleplay',
        'descripcion': 'Realiza diferentes acciones con GIFs',
        'comandos': [
            ('!act', 'Ver todas las acciones disponibles'),
            ('!act dance', 'Bailar al ritmo'),
            ('!act cook', 'Cocinar algo delicioso'),
            ('!act sleep', 'Dormir plácidamente'),
            ('!act cry', 'Llorar tristemente'),
            ('!act laugh', 'Reír a carcajadas'),
            ('!act jump', 'Saltar con emoción'),
            ('!act sing', 'Cantar con el corazón'),
            ('!act eat', 'Comer con gusto'),
            ('!act dab', 'Hacer un dab')
        ]
    },
    'animals': {
        'nombre': '🐾 Animales Adorables',
        'descripcion': 'Imágenes aleatorias de animales tiernos',
        'comandos': [
            ('!animals', 'Ver todos los animales disponibles'),
            ('!animals cat', 'Imagen aleatoria de gatito'),
            ('!animals dog', 'Imagen aleatoria de perrito'),
            ('!animals fox', 'Imagen aleatoria de zorrito'),
            ('!animals bunny', 'Imagen aleatoria de conejito'),
            ('!animals duck', 'Imagen aleatoria de patito'),
            ('!animals panda', 'Imagen aleatoria de pandita'),
            ('!animals penguin', 'Imagen aleatoria de pingüino'),
            ('!animals otter', 'Imagen aleatoria de nutria'),
            ('!animals parrot', 'Imagen aleatoria de lorito'),
            ('!animals raccoon', 'Imagen aleatoria de mapache'),
            ('!animals snake', 'Imagen aleatoria de serpiente'),
            ('!animals turtle', 'Imagen aleatoria de tortuga'),
            ('!animals dolphin', 'Imagen aleatoria de delfín')
        ]
    }
}

def test_real_categories():
    """Probar las categorías reales del bot"""
    print('🔍 PRUEBA DE CATEGORÍAS REALES DEL BOT')
    print('=' * 60)
    
    problematic_categories = []
    
    for categoria_key, seccion in categorias_reales.items():
        # Simular el embed que se crearía
        title = seccion['nombre']
        description = seccion['descripcion'] + '\n\n**Haz clic en los botones para ejecutar comandos directamente**'
        
        # Preparar fields
        fields = []
        
        # Comandos principales (aplicar límite de 8 como en el código real)
        comandos_principales = []
        max_comandos = 8 if len(seccion['comandos']) > 6 else len(seccion['comandos'])
        
        for i, (comando, descripcion_cmd) in enumerate(seccion['comandos'][:max_comandos]):
            # Acortar descripciones muy largas (como en el código real)
            desc_corta = descripcion_cmd[:50] + "..." if len(descripcion_cmd) > 50 else descripcion_cmd
            comandos_principales.append(f'`{comando}` - {desc_corta}')
        
        if len(comandos_principales) > 4:
            mid = len(comandos_principales) // 2
            fields.append(('🎯 Comandos (1/2)', '\n'.join(comandos_principales[:mid])))
            fields.append(('🎯 Comandos (2/2)', '\n'.join(comandos_principales[mid:])))
        else:
            fields.append(('🎯 Comandos Principales', '\n'.join(comandos_principales)))
        
        # Información específica según la sección (como en el código real)
        if categoria_key == 'counter':
            fields.append(('🔧 Estado del Sistema', '**Counter Engine:** ✅ Disponible\n**Modos:** ✅ Todos activos\n**Estadísticas:** ✅ Funcional'))
            fields.append(('💡 Tip Rápido', 'Usa `!setupcounter classic` para empezar\no `!counterhelp` para ayuda completa'))
        elif categoria_key == 'tts':
            fields.append(('🎤 Estado TTS', '**Engine:** ✅ Disponible\n**Voces:** ✅ Múltiples idiomas\n**Queue:** ✅ Funcional'))
            fields.append(('💡 Tip', 'Usa `!paimontts` para activar\no `!ttsvoices` para ver opciones'))
        elif categoria_key == 'economia':
            fields.append(('💰 Tu Estado', 'Usa el botón **💰 Mi Balance** para ver tu dinero actual'))
            fields.append(('🎰 Casino', 'Juega **Blackjack** o **Crash** para ganar dinero'))
        elif categoria_key == 'reactions':
            fields.append(('😊 Emociones', 'Expresa tus sentimientos con GIFs animados'))
            fields.append(('💡 Tip', 'Usa `!react` para ver todas las opciones disponibles'))
        elif categoria_key == 'actions':
            fields.append(('🎭 Roleplay', 'Realiza acciones divertidas con otros usuarios'))
            fields.append(('💡 Tip', 'Usa `!act` para ver todas las acciones disponibles'))
        elif categoria_key == 'animals':
            fields.append(('🐾 Animales', 'Imágenes adorables de diferentes animales'))
            fields.append(('💡 Tip', 'Usa `!animals` para ver todos los animales disponibles'))
        
        # Navegación (como en el código real)
        fields.append(('🔄 Navegación', '**Página actual:** 1/3\nUsa **🔙 Volver** para regresar al menú principal'))
        
        footer = '🎮 Haz clic en los botones para ejecutar comandos • Botón \'Volver\' para menú principal'
        
        # Verificar límites
        limites = check_embed_limits_simple(title, description, fields, footer)
        
        status = '✅' if limites['within_limits'] else '❌'
        print(f'{status} {seccion["nombre"]}: {limites["total_chars"]} caracteres')
        
        if not limites['within_limits']:
            problematic_categories.append({
                'name': seccion["nombre"],
                'key': categoria_key,
                'chars': limites["total_chars"],
                'excess': limites["total_chars"] - 5500,
                'fields': limites["field_count"]
            })
            print(f'   ⚠️  EXCEDE EL LÍMITE SEGURO: {limites["total_chars"] - 5500} caracteres de más')
            print(f'   📊 Fields: {limites["field_count"]}/25')
    
    if problematic_categories:
        print(f'\n❌ CATEGORÍAS PROBLEMÁTICAS ENCONTRADAS: {len(problematic_categories)}')
        print('=' * 60)
        
        for cat in problematic_categories:
            print(f'🚨 {cat["name"]} ({cat["key"]}):')
            print(f'   • Total: {cat["chars"]} caracteres')
            print(f'   • Exceso: {cat["excess"]} caracteres')
            print(f'   • Fields: {cat["fields"]}/25')
            print()
        
        print('🔧 SOLUCIONES RECOMENDADAS:')
        print('1. Reducir descripciones de comandos a máximo 30 caracteres')
        print('2. Limitar comandos mostrados a máximo 6 por categoría')
        print('3. Usar abreviaciones en nombres de fields')
        print('4. Reducir información adicional en fields específicos')
        print('5. Implementar sub-paginación para categorías con muchos comandos')
    else:
        print('\n✅ TODAS LAS CATEGORÍAS ESTÁN DENTRO DE LOS LÍMITES')
        print('El comando !com está correctamente optimizado.')

if __name__ == '__main__':
    test_real_categories()
