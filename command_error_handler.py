#!/usr/bin/env python3
"""
Sistema Avanzado de Manejo de Errores para Comandos del Bot Paimon
Implementa logging comprehensivo y recuperación automática de errores
"""

import logging
import traceback
import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import discord
from discord.ext import commands

# Configuración de logging avanzado
class ColoredFormatter(logging.Formatter):
    """Formatter con colores para diferentes niveles de log"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)

# Configurar logger principal
def setup_logging():
    """Configura el sistema de logging avanzado"""
    
    # Crear directorio de logs si no existe
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Logger principal
    logger = logging.getLogger('paimon_bot')
    logger.setLevel(logging.DEBUG)
    
    # Handler para archivo general
    file_handler = logging.FileHandler(
        f'logs/bot_{datetime.now().strftime("%Y%m%d")}.log',
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # Handler para errores críticos
    error_handler = logging.FileHandler(
        f'logs/errors_{datetime.now().strftime("%Y%m%d")}.log',
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    
    # Handler para consola con colores
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(ColoredFormatter(
        '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
    ))
    
    # Formatter para archivos
    file_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    error_handler.setFormatter(file_formatter)
    
    # Agregar handlers
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)
    logger.addHandler(console_handler)
    
    return logger

# Logger global
logger = setup_logging()

class ErrorTracker:
    """Sistema de seguimiento de errores"""
    
    def __init__(self):
        self.error_counts = defaultdict(int)
        self.recent_errors = deque(maxlen=100)
        self.command_failures = defaultdict(list)
        self.user_errors = defaultdict(int)
        self.start_time = datetime.now()
    
    def track_error(self, error_type: str, command: str, user_id: int, details: str):
        """Registra un error en el sistema de seguimiento"""
        error_data = {
            'timestamp': datetime.now(),
            'type': error_type,
            'command': command,
            'user_id': user_id,
            'details': details
        }
        
        self.error_counts[error_type] += 1
        self.recent_errors.append(error_data)
        self.command_failures[command].append(error_data)
        self.user_errors[user_id] += 1
        
        # Log del error
        logger.error(f"ERROR_TRACKED: {error_type} en {command} por usuario {user_id}: {details}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas de errores"""
        uptime = datetime.now() - self.start_time
        
        return {
            'uptime_hours': uptime.total_seconds() / 3600,
            'total_errors': sum(self.error_counts.values()),
            'error_types': dict(self.error_counts),
            'most_problematic_commands': self._get_top_failing_commands(),
            'users_with_most_errors': self._get_top_error_users(),
            'recent_errors': list(self.recent_errors)[-10:]  # Últimos 10
        }
    
    def _get_top_failing_commands(self, limit: int = 5) -> List[tuple]:
        """Obtiene los comandos con más errores"""
        command_error_counts = {
            cmd: len(errors) for cmd, errors in self.command_failures.items()
        }
        return sorted(command_error_counts.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def _get_top_error_users(self, limit: int = 5) -> List[tuple]:
        """Obtiene los usuarios con más errores"""
        return sorted(self.user_errors.items(), key=lambda x: x[1], reverse=True)[:limit]

# Instancia global del tracker
error_tracker = ErrorTracker()

class EnhancedErrorHandler:
    """Manejador avanzado de errores para comandos"""
    
    def __init__(self, bot):
        self.bot = bot
        self.recovery_strategies = {
            'discord.Forbidden': self._handle_permission_error,
            'discord.NotFound': self._handle_not_found_error,
            'discord.HTTPException': self._handle_http_error,
            'ValidationError': self._handle_validation_error,
            'DatabaseError': self._handle_database_error,
            'TimeoutError': self._handle_timeout_error,
        }
    
    async def handle_command_error(self, ctx, error, command_name: str):
        """Maneja errores de comandos con estrategias específicas"""
        
        error_type = type(error).__name__
        user_id = ctx.author.id
        
        # Registrar error
        error_tracker.track_error(
            error_type=error_type,
            command=command_name,
            user_id=user_id,
            details=str(error)
        )
        
        # Intentar recuperación específica
        if error_type in self.recovery_strategies:
            try:
                await self.recovery_strategies[error_type](ctx, error)
                logger.info(f"RECOVERY_SUCCESS: {error_type} en {command_name}")
                return True
            except Exception as recovery_error:
                logger.error(f"RECOVERY_FAILED: {error_type} -> {recovery_error}")
        
        # Manejo genérico
        await self._handle_generic_error(ctx, error, command_name)
        return False
    
    async def _handle_permission_error(self, ctx, error):
        """Maneja errores de permisos"""
        embed = discord.Embed(
            title="🚫 Permisos Insuficientes",
            description="El bot necesita más permisos para ejecutar esta acción.",
            color=0xFF6B00
        )
        
        # Sugerir permisos específicos
        if "send_messages" in str(error).lower():
            embed.add_field(
                name="Permiso Requerido",
                value="Enviar Mensajes",
                inline=False
            )
        elif "manage_channels" in str(error).lower():
            embed.add_field(
                name="Permiso Requerido",
                value="Gestionar Canales",
                inline=False
            )
        
        embed.add_field(
            name="💡 Solución",
            value="Contacta a un administrador para ajustar los permisos del bot.",
            inline=False
        )
        
        try:
            await ctx.send(embed=embed)
        except:
            # Si no puede enviar embed, intentar mensaje simple
            await ctx.send("🚫 El bot necesita más permisos para esta acción.")
    
    async def _handle_not_found_error(self, ctx, error):
        """Maneja errores de recursos no encontrados"""
        embed = discord.Embed(
            title="❓ Recurso No Encontrado",
            description="El elemento solicitado no existe o ya no está disponible.",
            color=0xFFFF00
        )
        
        embed.add_field(
            name="🔍 Posibles Causas",
            value="• El usuario/canal/rol fue eliminado\n• ID incorrecto\n• Permisos insuficientes",
            inline=False
        )
        
        embed.add_field(
            name="💡 Solución",
            value="Verifica que el elemento existe y que tienes permisos para acceder a él.",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    async def _handle_http_error(self, ctx, error):
        """Maneja errores HTTP de Discord"""
        embed = discord.Embed(
            title="🌐 Error de Conexión",
            description="Hubo un problema de comunicación con Discord.",
            color=0xFF9900
        )
        
        if "rate limited" in str(error).lower():
            embed.add_field(
                name="⏱️ Rate Limit",
                value="El bot está siendo limitado por Discord. Espera un momento.",
                inline=False
            )
        else:
            embed.add_field(
                name="🔄 Reintento",
                value="Intenta el comando nuevamente en unos segundos.",
                inline=False
            )
        
        await ctx.send(embed=embed)
    
    async def _handle_validation_error(self, ctx, error):
        """Maneja errores de validación"""
        embed = discord.Embed(
            title="❌ Error de Validación",
            description=str(error),
            color=0xFF0000
        )
        
        embed.add_field(
            name="💡 Ayuda",
            value="Usa `!ayuda <comando>` para ver la sintaxis correcta.",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    async def _handle_database_error(self, ctx, error):
        """Maneja errores de base de datos"""
        embed = discord.Embed(
            title="💾 Error de Base de Datos",
            description="Hubo un problema al acceder a los datos.",
            color=0xFF0066
        )
        
        embed.add_field(
            name="🔄 Acción Automática",
            value="El sistema intentará recuperarse automáticamente.",
            inline=False
        )
        
        await ctx.send(embed=embed)
        
        # Intentar reconexión a la base de datos
        # Aquí iría la lógica de reconexión
    
    async def _handle_timeout_error(self, ctx, error):
        """Maneja errores de timeout"""
        embed = discord.Embed(
            title="⏰ Tiempo Agotado",
            description="La operación tardó demasiado en completarse.",
            color=0xFFCC00
        )
        
        embed.add_field(
            name="🔄 Reintento",
            value="Puedes intentar el comando nuevamente.",
            inline=False
        )
        
        await ctx.send(embed=embed)
    
    async def _handle_generic_error(self, ctx, error, command_name: str):
        """Manejo genérico de errores"""
        embed = discord.Embed(
            title="💥 Error Inesperado",
            description="Ocurrió un error inesperado al ejecutar el comando.",
            color=0xFF0000
        )
        
        embed.add_field(
            name="🔧 Información Técnica",
            value=f"```{str(error)[:100]}...```" if len(str(error)) > 100 else f"```{str(error)}```",
            inline=False
        )
        
        embed.add_field(
            name="📞 Soporte",
            value="Si el problema persiste, contacta al administrador del bot.",
            inline=False
        )
        
        embed.set_footer(text=f"Error ID: {datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        await ctx.send(embed=embed)
        
        # Log detallado
        logger.error(f"UNHANDLED_ERROR en {command_name}: {error}")
        logger.error(traceback.format_exc())

# Función para crear el manejador de errores mejorado
def create_enhanced_error_handler(bot):
    """Crea y configura el manejador de errores mejorado"""
    
    error_handler = EnhancedErrorHandler(bot)
    
    @bot.event
    async def on_command_error(ctx, error):
        """Event handler para errores de comandos"""
        
        # Ignorar errores de comandos no encontrados
        if isinstance(error, commands.CommandNotFound):
            return
        
        # Obtener nombre del comando
        command_name = ctx.command.name if ctx.command else "unknown"
        
        # Manejar el error
        await error_handler.handle_command_error(ctx, error, command_name)
    
    return error_handler

# Comando para ver estadísticas de errores
async def error_stats_command(ctx):
    """Comando para ver estadísticas de errores (solo admin)"""
    
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("🚫 Solo los administradores pueden ver las estadísticas de errores.")
        return
    
    stats = error_tracker.get_error_stats()
    
    embed = discord.Embed(
        title="📊 Estadísticas de Errores del Bot",
        color=0x00FF00 if stats['total_errors'] < 10 else 0xFFFF00 if stats['total_errors'] < 50 else 0xFF0000
    )
    
    embed.add_field(
        name="⏱️ Tiempo Activo",
        value=f"{stats['uptime_hours']:.1f} horas",
        inline=True
    )
    
    embed.add_field(
        name="🚨 Total de Errores",
        value=str(stats['total_errors']),
        inline=True
    )
    
    embed.add_field(
        name="📈 Tasa de Errores",
        value=f"{stats['total_errors'] / max(stats['uptime_hours'], 1):.2f}/hora",
        inline=True
    )
    
    # Comandos más problemáticos
    if stats['most_problematic_commands']:
        problematic = '\n'.join([
            f"• {cmd}: {count} errores" 
            for cmd, count in stats['most_problematic_commands'][:5]
        ])
        embed.add_field(
            name="🔴 Comandos Problemáticos",
            value=problematic,
            inline=False
        )
    
    # Tipos de errores
    if stats['error_types']:
        error_types = '\n'.join([
            f"• {error_type}: {count}" 
            for error_type, count in list(stats['error_types'].items())[:5]
        ])
        embed.add_field(
            name="📋 Tipos de Errores",
            value=error_types,
            inline=False
        )
    
    embed.set_footer(text=f"Última actualización: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    await ctx.send(embed=embed)

if __name__ == "__main__":
    print("🔧 Sistema de Manejo de Errores Avanzado")
    print("=" * 50)
    print("✅ Características:")
    print("  • Logging con colores y niveles")
    print("  • Seguimiento de errores en tiempo real")
    print("  • Estrategias de recuperación automática")
    print("  • Estadísticas detalladas")
    print("  • Mensajes de error informativos")
    print("\n🚀 Sistema listo para integrar con el bot")
