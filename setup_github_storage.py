#!/usr/bin/env python3
"""
Configurador de Almacenamiento en GitHub para Paimon Bot
Configura GitHub como almacenamiento principal para datos cifrados
"""

import os
import sys

def check_github_config():
    """Verificar configuración de GitHub"""
    print("🔍 Verificando configuración de GitHub...")

    required_vars = {
        'GITHUB_TOKEN': 'Token personal de GitHub',
        'GITHUB_REPO_OWNER': 'Tu usuario de GitHub',
        'GITHUB_REPO_NAME': 'Nombre del repositorio',
    }

    optional_vars = {
        'GITHUB_BRANCH': 'Rama del repositorio (default: main)'
    }

    missing_vars = []

    print("\n📋 Variables requeridas:")
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # Mostrar solo los primeros y últimos caracteres del token
            if 'TOKEN' in var and len(value) > 8:
                display_value = f"{value[:4]}...{value[-4:]}"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: No configurado - {description}")
            missing_vars.append(var)

    print("\n📋 Variables opcionales:")
    for var, description in optional_vars.items():
        value = os.getenv(var, 'main' if 'BRANCH' in var else 'No configurado')
        print(f"ℹ️ {var}: {value}")

    return len(missing_vars) == 0, missing_vars

def show_github_setup_instructions():
    """Mostrar instrucciones para configurar GitHub"""
    print("\n" + "=" * 60)
    print("🌐 CONFIGURACIÓN DE GITHUB COMO ALMACENAMIENTO")
    print("=" * 60)

    print("\n📝 Paso 1: Crear Token Personal de GitHub")
    print("1. Ve a: https://github.com/settings/tokens")
    print("2. Click en 'Generate new token (classic)'")
    print("3. Selecciona estos permisos:")
    print("   ✅ repo (Full control of private repositories)")
    print("   ✅ public_repo (Access public repositories)")
    print("4. Copia el token generado")

    print("\n📝 Paso 2: Crear/Usar Repositorio")
    print("1. Crea un repositorio en GitHub (puede ser privado)")
    print("2. Anota el nombre del repositorio")
    print("3. Anota tu usuario de GitHub")

    print("\n📝 Paso 3: Configurar Variables en Replit")
    print("1. Ve a la pestaña 'Secrets' en Replit")
    print("2. Agrega estas variables:")
    print("   • GITHUB_TOKEN=tu_token_personal")
    print("   • GITHUB_REPO_OWNER=tu_usuario_github")
    print("   • GITHUB_REPO_NAME=nombre_del_repositorio")
    print("   • GITHUB_BRANCH=main (opcional)")

    print("\n📝 Paso 4: Verificar Configuración")
    print("1. Ejecuta: python setup_github_storage.py")
    print("2. Debería mostrar todas las variables configuradas")

    print("\n🔐 Estructura en GitHub:")
    print("https://github.com/Fl0zWer/Paimon/")
    print("└── data/")
    print("    ├── *********.Paimon")
    print("    ├── *********.Paimon")
    print("    └── ...")

def test_github_connection():
    """Probar conexión con GitHub"""
    print("\n🔗 Probando conexión con GitHub...")

    try:
        import requests

        github_token = os.getenv('GITHUB_TOKEN')
        github_owner = os.getenv('GITHUB_REPO_OWNER')
        github_repo = os.getenv('GITHUB_REPO_NAME')

        if not all([github_token, github_owner, github_repo]):
            print("❌ Variables de GitHub no configuradas")
            return False

        # Probar acceso al repositorio
        url = f"https://api.github.com/repos/{github_owner}/{github_repo}"
        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            repo_data = response.json()
            print(f"✅ Conexión exitosa con repositorio: {repo_data['full_name']}")
            print(f"   • Privado: {'Sí' if repo_data['private'] else 'No'}")
            print(f"   • Rama por defecto: {repo_data['default_branch']}")
            return True
        elif response.status_code == 404:
            print("❌ Repositorio no encontrado o sin acceso")
            print("   Verifica el nombre del repositorio y los permisos del token")
            return False
        elif response.status_code == 401:
            print("❌ Token de GitHub inválido o sin permisos")
            print("   Verifica que el token tenga permisos 'repo'")
            return False
        else:
            print(f"❌ Error de conexión: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            return False

    except ImportError:
        print("❌ Módulo 'requests' no disponible")
        print("   Instala con: pip install requests")
        return False
    except Exception as e:
        print(f"❌ Error probando conexión: {e}")
        return False

def create_github_folder():
    """Crear carpeta data en GitHub si no existe"""
    print("\n📁 Verificando estructura de carpetas en GitHub...")

    try:
        import requests
        import base64

        github_token = os.getenv('GITHUB_TOKEN')
        github_owner = os.getenv('GITHUB_REPO_OWNER')
        github_repo = os.getenv('GITHUB_REPO_NAME')
        github_branch = os.getenv('GITHUB_BRANCH', 'main')

        # Verificar si la carpeta data/ ya existe
        url_check = f"https://api.github.com/repos/{github_owner}/{github_repo}/contents/data"
        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }

        response = requests.get(url_check, headers=headers)

        if response.status_code == 200:
            print("✅ Carpeta data/ ya existe en GitHub")
            return True

        # Crear archivo README en la carpeta data
        readme_content = """# Encrypted Users Data

This folder contains encrypted user data for Paimon Bot.

- All files are encrypted with AES-256-GCM
- Each file represents one user account
- Files are named with Discord user IDs (e.g., *********.Paimon)
- Data is protected with individual user passwords

⚠️ **Security Notice**: Even if someone accesses these files, they cannot read the data without the individual user passwords.

## File Structure
```
data/
├── README.md (this file)
├── *********.Paimon (encrypted user data)
├── *********.Paimon (encrypted user data)
└── ...
```

## Encryption Details
- **Algorithm**: AES-256-GCM (Galois/Counter Mode)
- **Key Derivation**: PBKDF2-SHA256 with 100,000 iterations
- **Salt**: 16 bytes unique per user
- **IV**: 12 bytes unique per encryption
- **Authentication**: Built-in auth tag for integrity verification
"""

        content_b64 = base64.b64encode(readme_content.encode('utf-8')).decode('utf-8')

        url = f"https://api.github.com/repos/{github_owner}/{github_repo}/contents/data/README.md"
        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        }

        payload = {
            'message': 'Create data folder with README for encrypted user data',
            'content': content_b64,
            'branch': github_branch
        }

        response = requests.put(url, headers=headers, json=payload)

        if response.status_code in [200, 201]:
            print("✅ Carpeta data/ configurada exitosamente")
            return True
        elif response.status_code == 422:
            print("ℹ️ Carpeta data/ ya existe")
            return True
        else:
            print(f"⚠️ No se pudo configurar la carpeta: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error creando carpeta: {e}")
        return False

def main():
    print("🌐 Configurador de Almacenamiento en GitHub - Paimon Bot")
    print("=" * 60)

    # Verificar configuración
    config_ok, missing_vars = check_github_config()

    if not config_ok:
        print(f"\n❌ Faltan {len(missing_vars)} variables de configuración")
        show_github_setup_instructions()
        return

    print("\n✅ Todas las variables requeridas están configuradas")

    # Probar conexión
    if test_github_connection():
        print("\n🎉 GitHub configurado correctamente como almacenamiento")

        # Crear estructura de carpetas
        create_github_folder()

        print("\n🚀 Próximos pasos:")
        print("1. Ejecuta tu bot: python main.py")
        print("2. Los datos se guardarán automáticamente en GitHub")
        print("3. Puedes verificar en: https://github.com/Fl0zWer/Paimon/tree/main/data")

    else:
        print("\n❌ Error de conexión con GitHub")
        show_github_setup_instructions()

if __name__ == "__main__":
    main()
